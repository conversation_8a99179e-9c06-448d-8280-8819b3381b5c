[{"C:\\laragon\\www\\react-news\\frontend\\src\\index.js": "1", "C:\\laragon\\www\\react-news\\frontend\\src\\App.js": "2", "C:\\laragon\\www\\react-news\\frontend\\src\\reportWebVitals.js": "3", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\Dashboard.js": "4", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\News.js": "5", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\Settings.js": "6", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\LandingPage.js": "7", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\MainContent.js": "8", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\SettingsPage.js": "9", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\NewsPage.js": "10", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\HomePage.js": "11", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\WebsitePage.js": "12", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Navbar.js": "13", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Footer.js": "14", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\CategorySlider.js": "15", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\NewsCard.js": "16", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\PopularNews.js": "17", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\BottomAppBar.js": "18", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\Login.js": "19", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\Register.js": "20", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\swal.js": "21", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\TambahBerita.js": "22", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\EditNews.js": "23", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\PreviewNews.js": "24", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\MainNewsCards.js": "25", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Saved.js": "26"}, {"size": 535, "mtime": 1752195971363, "results": "27", "hashOfConfig": "28"}, {"size": 1177, "mtime": 1752292884757, "results": "29", "hashOfConfig": "28"}, {"size": 362, "mtime": 1752195971670, "results": "30", "hashOfConfig": "28"}, {"size": 15010, "mtime": 1752301874967, "results": "31", "hashOfConfig": "28"}, {"size": 400, "mtime": 1752196916887, "results": "32", "hashOfConfig": "28"}, {"size": 344, "mtime": 1752196916887, "results": "33", "hashOfConfig": "28"}, {"size": 46669, "mtime": 1752293723100, "results": "34", "hashOfConfig": "28"}, {"size": 308, "mtime": 1752197903559, "results": "35", "hashOfConfig": "28"}, {"size": 7250, "mtime": 1752219364538, "results": "36", "hashOfConfig": "28"}, {"size": 28077, "mtime": 1752304722880, "results": "37", "hashOfConfig": "28"}, {"size": 9504, "mtime": 1752304186867, "results": "38", "hashOfConfig": "28"}, {"size": 243, "mtime": 1752201497899, "results": "39", "hashOfConfig": "28"}, {"size": 662, "mtime": 1752201773682, "results": "40", "hashOfConfig": "28"}, {"size": 313, "mtime": 1752201773682, "results": "41", "hashOfConfig": "28"}, {"size": 822, "mtime": 1752201773678, "results": "42", "hashOfConfig": "28"}, {"size": 688, "mtime": 1752201773679, "results": "43", "hashOfConfig": "28"}, {"size": 749, "mtime": 1752201773681, "results": "44", "hashOfConfig": "28"}, {"size": 2125, "mtime": 1752226192122, "results": "45", "hashOfConfig": "28"}, {"size": 2994, "mtime": 1752207533369, "results": "46", "hashOfConfig": "28"}, {"size": 4480, "mtime": 1752207178102, "results": "47", "hashOfConfig": "28"}, {"size": 218, "mtime": 1752206025063, "results": "48", "hashOfConfig": "28"}, {"size": 5855, "mtime": 1752222676319, "results": "49", "hashOfConfig": "28"}, {"size": 4418, "mtime": 1752214317215, "results": "50", "hashOfConfig": "28"}, {"size": 2744, "mtime": 1752218351473, "results": "51", "hashOfConfig": "28"}, {"size": 4157, "mtime": 1752220101050, "results": "52", "hashOfConfig": "28"}, {"size": 5156, "mtime": 1752232948251, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1umod7j", {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\laragon\\www\\react-news\\frontend\\src\\index.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\App.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\reportWebVitals.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\Dashboard.js", ["132"], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\News.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\Settings.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\LandingPage.js", ["133"], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\MainContent.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\SettingsPage.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\NewsPage.js", ["134", "135", "136", "137"], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\HomePage.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\WebsitePage.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Navbar.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Footer.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\CategorySlider.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\NewsCard.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\PopularNews.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\BottomAppBar.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\Login.js", ["138", "139"], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\Register.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\swal.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\TambahBerita.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\EditNews.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\pages\\PreviewNews.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\MainNewsCards.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Saved.js", ["140"], [], {"ruleId": "141", "severity": 1, "message": "142", "line": 48, "column": 9, "nodeType": "143", "messageId": "144", "endLine": 48, "endColumn": 17}, {"ruleId": "141", "severity": 1, "message": "145", "line": 16, "column": 8, "nodeType": "143", "messageId": "144", "endLine": 16, "endColumn": 14}, {"ruleId": "141", "severity": 1, "message": "146", "line": 5, "column": 8, "nodeType": "143", "messageId": "144", "endLine": 5, "endColumn": 11}, {"ruleId": "141", "severity": 1, "message": "147", "line": 20, "column": 8, "nodeType": "143", "messageId": "144", "endLine": 20, "endColumn": 20}, {"ruleId": "141", "severity": 1, "message": "148", "line": 56, "column": 11, "nodeType": "143", "messageId": "144", "endLine": 56, "endColumn": 14}, {"ruleId": "141", "severity": 1, "message": "149", "line": 183, "column": 9, "nodeType": "143", "messageId": "144", "endLine": 183, "endColumn": 18}, {"ruleId": "141", "severity": 1, "message": "150", "line": 11, "column": 10, "nodeType": "143", "messageId": "144", "endLine": 11, "endColumn": 18}, {"ruleId": "141", "severity": 1, "message": "151", "line": 12, "column": 9, "nodeType": "143", "messageId": "144", "endLine": 12, "endColumn": 17}, {"ruleId": "141", "severity": 1, "message": "152", "line": 4, "column": 8, "nodeType": "143", "messageId": "144", "endLine": 4, "endColumn": 23}, "no-unused-vars", "'isMobile' is assigned a value but never used.", "Identifier", "unusedVar", "'Footer' is defined but never used.", "'Box' is defined but never used.", "'MoreVertIcon' is defined but never used.", "'thn' is assigned a value but never used.", "'emptyRows' is assigned a value but never used.", "'jwtToken' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'BookmarkAddIcon' is defined but never used."]