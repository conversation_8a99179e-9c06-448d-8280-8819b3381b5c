[{"C:\\laragon\\www\\react-news\\frontend\\src\\index.js": "1", "C:\\laragon\\www\\react-news\\frontend\\src\\App.js": "2", "C:\\laragon\\www\\react-news\\frontend\\src\\reportWebVitals.js": "3", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\News.js": "4", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\Settings.js": "5", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\LandingPage.js": "6", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\WebsitePage.js": "7", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Navbar.js": "8", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Footer.js": "9", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\CategorySlider.js": "10", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\NewsCard.js": "11", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\PopularNews.js": "12", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\BottomAppBar.js": "13", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\Login.js": "14", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\Register.js": "15", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\swal.js": "16", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\MainNewsCards.js": "17", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Saved.js": "18", "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\views.js": "19"}, {"size": 535, "mtime": 1752195971363, "results": "20", "hashOfConfig": "21"}, {"size": 758, "mtime": 1752305005247, "results": "22", "hashOfConfig": "21"}, {"size": 362, "mtime": 1752195971670, "results": "23", "hashOfConfig": "21"}, {"size": 400, "mtime": 1752196916887, "results": "24", "hashOfConfig": "21"}, {"size": 344, "mtime": 1752196916887, "results": "25", "hashOfConfig": "21"}, {"size": 46669, "mtime": 1752293723100, "results": "26", "hashOfConfig": "21"}, {"size": 243, "mtime": 1752201497899, "results": "27", "hashOfConfig": "21"}, {"size": 662, "mtime": 1752201773682, "results": "28", "hashOfConfig": "21"}, {"size": 313, "mtime": 1752201773682, "results": "29", "hashOfConfig": "21"}, {"size": 822, "mtime": 1752201773678, "results": "30", "hashOfConfig": "21"}, {"size": 688, "mtime": 1752201773679, "results": "31", "hashOfConfig": "21"}, {"size": 749, "mtime": 1752201773681, "results": "32", "hashOfConfig": "21"}, {"size": 2125, "mtime": 1752226192122, "results": "33", "hashOfConfig": "21"}, {"size": 2994, "mtime": 1752207533369, "results": "34", "hashOfConfig": "21"}, {"size": 4480, "mtime": 1752207178102, "results": "35", "hashOfConfig": "21"}, {"size": 218, "mtime": 1752206025063, "results": "36", "hashOfConfig": "21"}, {"size": 4157, "mtime": 1752220101050, "results": "37", "hashOfConfig": "21"}, {"size": 5156, "mtime": 1752232948251, "results": "38", "hashOfConfig": "21"}, {"size": 31009, "mtime": 1752305460719, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1umod7j", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\laragon\\www\\react-news\\frontend\\src\\index.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\App.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\reportWebVitals.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\News.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\Settings.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\LandingPage.js", ["97"], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\WebsitePage.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Navbar.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Footer.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\CategorySlider.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\NewsCard.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\PopularNews.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\BottomAppBar.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\Login.js", ["98", "99"], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\auth\\Register.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\swal.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\MainNewsCards.js", [], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\user\\components\\Saved.js", ["100"], [], "C:\\laragon\\www\\react-news\\frontend\\src\\pages\\admin\\views.js", ["101"], [], {"ruleId": "102", "severity": 1, "message": "103", "line": 16, "column": 8, "nodeType": "104", "messageId": "105", "endLine": 16, "endColumn": 14}, {"ruleId": "102", "severity": 1, "message": "106", "line": 11, "column": 10, "nodeType": "104", "messageId": "105", "endLine": 11, "endColumn": 18}, {"ruleId": "102", "severity": 1, "message": "107", "line": 12, "column": 9, "nodeType": "104", "messageId": "105", "endLine": 12, "endColumn": 17}, {"ruleId": "102", "severity": 1, "message": "108", "line": 4, "column": 8, "nodeType": "104", "messageId": "105", "endLine": 4, "endColumn": 23}, {"ruleId": "102", "severity": 1, "message": "109", "line": 29, "column": 3, "nodeType": "104", "messageId": "105", "endLine": 29, "endColumn": 10}, "no-unused-vars", "'Footer' is defined but never used.", "Identifier", "unusedVar", "'jwtToken' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'BookmarkAddIcon' is defined but never used.", "'Divider' is defined but never used."]