{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\pages\\\\admin\\\\pages\\\\NewsPage.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport Button from '@mui/material/Button';\nimport AddIcon from '@mui/icons-material/Add';\nimport { useNavigate } from 'react-router-dom';\nimport React, { useEffect, useState, useMemo } from 'react';\nimport Box from '@mui/material/Box';\nimport Breadcrumbs from '@mui/material/Breadcrumbs';\nimport Link from '@mui/material/Link';\nimport Typography from '@mui/material/Typography';\nimport useMediaQuery from '@mui/material/useMediaQuery';\nimport { useTheme } from '@mui/material/styles';\nimport Avatar from '@mui/material/Avatar';\nimport IconButton from '@mui/material/IconButton';\nimport EditIcon from '@mui/icons-material/Edit';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport VisibilityIcon from '@mui/icons-material/Visibility';\nimport Swal from 'sweetalert2';\nimport Checkbox from '@mui/material/Checkbox';\nimport SearchIcon from '@mui/icons-material/Search';\nimport FilterListIcon from '@mui/icons-material/FilterList';\nimport MoreVertIcon from '@mui/icons-material/MoreVert';\nimport Chip from '@mui/material/Chip';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction DeskripsiCell({\n  text,\n  maxLength,\n  isMobile\n}) {\n  _s();\n  const [expanded, setExpanded] = useState(false);\n  if (!text) return '';\n  const isLong = text.length > maxLength;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col gap-1\",\n    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n      className: `${isMobile ? 'text-xs' : 'text-sm'} text-gray-700 leading-tight ${expanded ? 'whitespace-normal' : 'truncate'}`,\n      children: expanded || !isLong ? text : text.slice(0, maxLength) + '...'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), isLong && /*#__PURE__*/_jsxDEV(\"button\", {\n      className: `text-blue-600 hover:text-blue-800 text-xs font-medium self-start transition-colors`,\n      onClick: () => setExpanded(!expanded),\n      children: expanded ? 'Tutup' : 'Baca'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n}\n\n// Mobile Card Component\n_s(DeskripsiCell, \"DuL5jiiQQFgbn7gBKAyxwS/H4Ek=\");\n_c = DeskripsiCell;\nfunction MobileNewsCard({\n  row,\n  idx,\n  checked,\n  handleClick,\n  handleEdit,\n  handleDelete,\n  navigate,\n  page,\n  rowsPerPage,\n  isMobile\n}) {\n  const formatDate = raw => {\n    if (!raw) return '';\n    const d = new Date(raw);\n    if (isNaN(d)) return raw;\n    const bulan = ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'];\n    const tgl = d.getDate();\n    const bln = bulan[d.getMonth()];\n    const thn = d.getFullYear();\n    const jam = d.getHours().toString().padStart(2, '0');\n    const menit = d.getMinutes().toString().padStart(2, '0');\n    return `${tgl}/${bln} ${jam}:${menit}`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `bg-white border border-gray-200 rounded-lg p-4 mb-3 transition-all duration-200 ${checked ? 'ring-2 ring-blue-500 bg-blue-50 shadow-lg' : 'hover:bg-gray-50 hover:shadow-md'}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n          color: \"primary\",\n          checked: checked,\n          onChange: () => handleClick(row.id),\n          inputProps: {\n            'aria-label': `select row ${idx + 1}`\n          },\n          sx: {\n            '&.Mui-checked': {\n              color: '#2563eb'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: `#${page * rowsPerPage + idx + 1}`,\n          size: \"small\",\n          sx: {\n            bgcolor: '#f1f5f9',\n            color: '#475569',\n            fontWeight: 600,\n            fontSize: '0.75rem'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full\",\n        children: formatDate(row.date || row.created_at)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-shrink-0\",\n        children: /*#__PURE__*/_jsxDEV(Avatar, {\n          alt: row.title,\n          src: row.image ? row.image.startsWith('/uploads/') ? row.image : `/uploads/${row.image}` : '',\n          sx: {\n            width: 60,\n            height: 60,\n            borderRadius: 2,\n            border: '2px solid #e2e8f0',\n            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n          },\n          variant: \"square\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 min-w-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"font-semibold text-gray-900 text-sm mb-1 truncate\",\n          children: row.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-gray-600 mb-3 line-clamp-2\",\n          children: row.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate(`/admin/pages/previewnews/${row.id}`),\n            className: \"flex items-center gap-1 px-3 py-1.5 text-xs bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-all duration-200 font-medium\",\n            children: [/*#__PURE__*/_jsxDEV(VisibilityIcon, {\n              sx: {\n                fontSize: 14\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this), \"Lihat\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleEdit(row.id),\n            className: \"flex items-center gap-1 px-3 py-1.5 text-xs bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-all duration-200 font-medium\",\n            children: [/*#__PURE__*/_jsxDEV(EditIcon, {\n              sx: {\n                fontSize: 14\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), \"Edit\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleDelete(row.id),\n            className: \"flex items-center gap-1 px-3 py-1.5 text-xs bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-all duration-200 font-medium\",\n            children: [/*#__PURE__*/_jsxDEV(DeleteIcon, {\n              sx: {\n                fontSize: 14\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), \"Hapus\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this);\n}\n_c2 = MobileNewsCard;\nexport default function NewsPage() {\n  _s2();\n  const [rows, setRows] = useState([]);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(10);\n  const [selected, setSelected] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const navigate = useNavigate();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n  useEffect(() => {\n    fetch('/api/posts').then(res => res.json()).then(data => {\n      if (Array.isArray(data)) {\n        setRows(data);\n      } else {\n        setRows([]);\n      }\n    }).catch(() => setRows([]));\n  }, []);\n\n  // Filter rows based on search term\n  const filteredRows = useMemo(() => {\n    if (!searchTerm) return rows;\n    return rows.filter(row => {\n      var _row$title, _row$description;\n      return ((_row$title = row.title) === null || _row$title === void 0 ? void 0 : _row$title.toLowerCase().includes(searchTerm.toLowerCase())) || ((_row$description = row.description) === null || _row$description === void 0 ? void 0 : _row$description.toLowerCase().includes(searchTerm.toLowerCase()));\n    });\n  }, [rows, searchTerm]);\n  const emptyRows = page > 0 ? Math.max(0, (1 + page) * rowsPerPage - filteredRows.length) : 0;\n  const visibleRows = useMemo(() => filteredRows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage), [filteredRows, page, rowsPerPage]);\n\n  // Checkbox logic\n  const isSelected = id => selected.indexOf(id) !== -1;\n  const handleSelectAllClick = event => {\n    if (event.target.checked) {\n      const newSelected = visibleRows.map(row => row.id);\n      setSelected([...new Set([...selected, ...newSelected])]);\n    } else {\n      const newSelected = selected.filter(id => !visibleRows.some(row => row.id === id));\n      setSelected(newSelected);\n    }\n  };\n  const handleClick = id => {\n    const selectedIndex = selected.indexOf(id);\n    let newSelected = [];\n    if (selectedIndex === -1) {\n      newSelected = newSelected.concat(selected, id);\n    } else {\n      newSelected = selected.filter(selId => selId !== id);\n    }\n    setSelected(newSelected);\n  };\n  const handleDeleteSelected = async () => {\n    if (selected.length === 0) return;\n    const confirm = await Swal.fire({\n      title: 'Hapus Berita?',\n      text: `Yakin ingin menghapus ${selected.length} berita terpilih?`,\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonText: 'Ya, hapus',\n      cancelButtonText: 'Batal',\n      confirmButtonColor: '#dc2626',\n      cancelButtonColor: '#6b7280'\n    });\n    if (confirm.isConfirmed) {\n      const token = localStorage.getItem('jwt');\n      for (const id of selected) {\n        await fetch(`/api/posts/${id}`, {\n          method: 'DELETE',\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n      }\n      setSelected([]);\n      // Refresh data\n      fetch('/api/posts').then(res => res.json()).then(data => {\n        if (Array.isArray(data)) {\n          setRows(data);\n        } else {\n          setRows([]);\n        }\n      }).catch(() => setRows([]));\n      Swal.fire({\n        title: 'Berhasil!',\n        text: `${selected.length} berita berhasil dihapus`,\n        icon: 'success',\n        timer: 2000,\n        showConfirmButton: false\n      });\n    }\n  };\n  const handleEdit = id => {\n    navigate(`/admin/pages/editnews/${id}`);\n  };\n  const handleDelete = async id => {\n    const confirm = await Swal.fire({\n      title: 'Hapus Berita?',\n      text: 'Yakin ingin menghapus berita ini?',\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonText: 'Ya, hapus',\n      cancelButtonText: 'Batal',\n      confirmButtonColor: '#dc2626',\n      cancelButtonColor: '#6b7280'\n    });\n    if (confirm.isConfirmed) {\n      const token = localStorage.getItem('jwt');\n      await fetch(`/api/posts/${id}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      // Refresh data\n      fetch('/api/posts').then(res => res.json()).then(data => {\n        if (Array.isArray(data)) {\n          setRows(data);\n        } else {\n          setRows([]);\n        }\n      }).catch(() => setRows([]));\n      Swal.fire({\n        title: 'Berhasil!',\n        text: 'Berita berhasil dihapus',\n        icon: 'success',\n        timer: 2000,\n        showConfirmButton: false\n      });\n    }\n  };\n  const formatDate = raw => {\n    if (!raw) return '';\n    const d = new Date(raw);\n    if (isNaN(d)) return raw;\n    const bulan = ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'];\n    const tgl = d.getDate();\n    const bln = bulan[d.getMonth()];\n    const thn = d.getFullYear();\n    const jam = d.getHours().toString().padStart(2, '0');\n    const menit = d.getMinutes().toString().padStart(2, '0');\n    return `${tgl}/${bln}/${thn} ${jam}:${menit}`;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full flex flex-col gap-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 md:p-6 bg-white rounded-lg border border-gray-200 shadow-sm\",\n      children: /*#__PURE__*/_jsxDEV(Breadcrumbs, {\n        \"aria-label\": \"breadcrumb\",\n        sx: {\n          fontSize: {\n            xs: '12px',\n            md: '14px'\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          underline: \"hover\",\n          color: \"inherit\",\n          href: \"/admin/dashboard\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          color: \"text.primary\",\n          children: \"Berita\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 md:p-6 border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl md:text-2xl font-bold text-gray-800 mb-1\",\n              children: \"Daftar Berita\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm\",\n              children: \"Kelola dan atur semua berita dalam sistem\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-3\",\n            children: [selected.length > 0 && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              color: \"error\",\n              onClick: handleDeleteSelected,\n              sx: {\n                fontWeight: 600,\n                borderRadius: 2,\n                fontSize: {\n                  xs: '12px',\n                  md: '14px'\n                },\n                py: {\n                  xs: 1,\n                  md: 1.5\n                },\n                px: {\n                  xs: 2,\n                  md: 3\n                }\n              },\n              children: [\"Hapus \", selected.length, \" Terpilih\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"primary\",\n              startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 28\n              }, this),\n              sx: {\n                borderRadius: 2,\n                textTransform: 'none',\n                fontWeight: 600,\n                fontSize: {\n                  xs: '12px',\n                  md: '14px'\n                },\n                py: {\n                  xs: 1,\n                  md: 1.5\n                },\n                px: {\n                  xs: 2,\n                  md: 3\n                },\n                boxShadow: '0 4px 6px -1px rgba(37, 99, 235, 0.2)',\n                '&:hover': {\n                  boxShadow: '0 6px 8px -1px rgba(37, 99, 235, 0.3)'\n                }\n              },\n              onClick: () => navigate('/admin/pages/tambahberita'),\n              children: \"Tambah Berita\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 md:p-6 bg-gray-50 border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 relative\",\n            children: [/*#__PURE__*/_jsxDEV(SearchIcon, {\n              sx: {\n                position: 'absolute',\n                left: 12,\n                top: '50%',\n                transform: 'translateY(-50%)',\n                color: '#9ca3af',\n                fontSize: 20\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Cari berita berdasarkan judul atau deskripsi...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              className: \"w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            startIcon: /*#__PURE__*/_jsxDEV(FilterListIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 26\n            }, this),\n            sx: {\n              borderRadius: 2,\n              textTransform: 'none',\n              fontWeight: 500,\n              fontSize: {\n                xs: '12px',\n                md: '14px'\n              },\n              py: {\n                xs: 1,\n                md: 1.5\n              },\n              px: {\n                xs: 2,\n                md: 3\n              }\n            },\n            children: \"Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 md:px-6 py-3 bg-white border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              sx: {\n                color: '#6b7280',\n                fontSize: {\n                  xs: '12px',\n                  md: '14px'\n                },\n                fontWeight: 500\n              },\n              children: [filteredRows.length, \" berita ditemukan\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this), selected.length > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n              label: `${selected.length} terpilih`,\n              size: \"small\",\n              color: \"primary\",\n              sx: {\n                fontSize: '0.75rem'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Baris per halaman:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: rowsPerPage,\n              onChange: e => {\n                setRowsPerPage(parseInt(e.target.value, 10));\n                setPage(0);\n              },\n              className: \"border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\",\n              children: [5, 10, 25, 50].map(option => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: option,\n                children: option\n              }, option, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden\",\n      children: [isMobile ?\n      /*#__PURE__*/\n      /* Mobile Card View */\n      _jsxDEV(\"div\", {\n        className: \"p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4 p-3 bg-gray-50 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n              color: \"primary\",\n              indeterminate: visibleRows.filter(row => isSelected(row.id)).length > 0 && visibleRows.filter(row => isSelected(row.id)).length < visibleRows.length,\n              checked: visibleRows.length > 0 && visibleRows.filter(row => isSelected(row.id)).length === visibleRows.length,\n              onChange: handleSelectAllClick,\n              inputProps: {\n                'aria-label': 'select all news'\n              },\n              sx: {\n                '&.Mui-checked': {\n                  color: '#2563eb'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-700\",\n              children: \"Pilih Semua\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 15\n          }, this), selected.length > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n            label: `${selected.length} terpilih`,\n            size: \"small\",\n            color: \"primary\",\n            sx: {\n              fontSize: '0.75rem'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 13\n        }, this), visibleRows.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-12 text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(SearchIcon, {\n              sx: {\n                fontSize: 48,\n                color: '#d1d5db',\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg font-medium mb-2\",\n              children: searchTerm ? 'Tidak ada hasil pencarian' : 'Belum ada berita'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm\",\n              children: searchTerm ? 'Coba ubah kata kunci pencarian' : 'Berita akan muncul di sini setelah ditambahkan'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 15\n        }, this) : visibleRows.map((row, idx) => {\n          const checked = isSelected(row.id);\n          return /*#__PURE__*/_jsxDEV(MobileNewsCard, {\n            row: row,\n            idx: idx,\n            checked: checked,\n            handleClick: handleClick,\n            handleEdit: handleEdit,\n            handleDelete: handleDelete,\n            navigate: navigate,\n            page: page,\n            rowsPerPage: rowsPerPage,\n            isMobile: isMobile\n          }, row.id || idx, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 19\n          }, this);\n        })]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 11\n      }, this) :\n      /*#__PURE__*/\n      /* Desktop Table View */\n      _jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gradient-to-r from-gray-800 to-gray-900 text-white\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"w-16 p-4 text-center\",\n                children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                  color: \"primary\",\n                  indeterminate: visibleRows.filter(row => isSelected(row.id)).length > 0 && visibleRows.filter(row => isSelected(row.id)).length < visibleRows.length,\n                  checked: visibleRows.length > 0 && visibleRows.filter(row => isSelected(row.id)).length === visibleRows.length,\n                  onChange: handleSelectAllClick,\n                  inputProps: {\n                    'aria-label': 'select all news'\n                  },\n                  sx: {\n                    color: '#fff',\n                    '&.Mui-checked': {\n                      color: '#fff'\n                    },\n                    '&.MuiCheckbox-indeterminate': {\n                      color: '#fff'\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 526,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"w-20 p-4 text-center font-semibold text-sm\",\n                children: \"No\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"w-24 p-4 text-left font-semibold text-sm\",\n                children: \"Gambar\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"min-w-[200px] p-4 text-left font-semibold text-sm\",\n                children: \"Judul\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 541,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"min-w-[300px] p-4 text-left font-semibold text-sm\",\n                children: \"Deskripsi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"w-40 p-4 text-left font-semibold text-sm\",\n                children: \"Tgl & Waktu\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"w-32 p-4 text-center font-semibold text-sm\",\n                children: \"Aksi\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white\",\n            children: visibleRows.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: /*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: \"7\",\n                className: \"p-12 text-center\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-gray-500\",\n                  children: [/*#__PURE__*/_jsxDEV(SearchIcon, {\n                    sx: {\n                      fontSize: 48,\n                      color: '#d1d5db',\n                      mb: 2\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 552,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-lg font-medium mb-2\",\n                    children: searchTerm ? 'Tidak ada hasil pencarian' : 'Belum ada berita'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 553,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm\",\n                    children: searchTerm ? 'Coba ubah kata kunci pencarian' : 'Berita akan muncul di sini setelah ditambahkan'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 556,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 19\n            }, this) : visibleRows.map((row, idx) => {\n              const checked = isSelected(row.id);\n              return /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: `border-b border-gray-100 transition-colors duration-200 ${checked ? 'bg-blue-50' : 'hover:bg-gray-50'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"w-16 p-4 text-center\",\n                  children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                    color: \"primary\",\n                    checked: checked,\n                    onChange: () => handleClick(row.id),\n                    inputProps: {\n                      'aria-label': `select row ${idx + 1}`\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 568,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"w-20 p-4 text-center\",\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    label: page * rowsPerPage + idx + 1,\n                    size: \"small\",\n                    sx: {\n                      bgcolor: '#f1f5f9',\n                      color: '#475569',\n                      fontWeight: 600,\n                      fontSize: '0.75rem'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 576,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 575,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"w-24 p-4\",\n                  children: /*#__PURE__*/_jsxDEV(Avatar, {\n                    alt: row.title,\n                    src: row.image ? row.image.startsWith('/uploads/') ? row.image : `/uploads/${row.image}` : '',\n                    sx: {\n                      width: 48,\n                      height: 48,\n                      borderRadius: 2,\n                      border: '2px solid #e2e8f0',\n                      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                    },\n                    variant: \"square\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 588,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"min-w-[200px] p-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm font-semibold text-gray-900 break-words\",\n                    children: row.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 601,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"min-w-[300px] p-4\",\n                  children: /*#__PURE__*/_jsxDEV(DeskripsiCell, {\n                    text: row.description,\n                    maxLength: 100,\n                    isMobile: isMobile\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 607,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 606,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"w-40 p-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded-full inline-block whitespace-nowrap\",\n                    children: formatDate(row.date || row.created_at)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 609,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"w-32 p-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-center gap-1\",\n                    children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                      color: \"info\",\n                      size: \"small\",\n                      onClick: () => navigate(`/admin/pages/previewnews/${row.id}`),\n                      sx: {\n                        bgcolor: '#eff6ff',\n                        '&:hover': {\n                          bgcolor: '#dbeafe'\n                        },\n                        width: 32,\n                        height: 32,\n                        borderRadius: 1.5\n                      },\n                      children: /*#__PURE__*/_jsxDEV(VisibilityIcon, {\n                        sx: {\n                          fontSize: 16\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 628,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 616,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                      color: \"primary\",\n                      size: \"small\",\n                      onClick: () => handleEdit(row.id),\n                      sx: {\n                        bgcolor: '#f0fdf4',\n                        '&:hover': {\n                          bgcolor: '#dcfce7'\n                        },\n                        width: 32,\n                        height: 32,\n                        borderRadius: 1.5\n                      },\n                      children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                        sx: {\n                          fontSize: 16\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 642,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 630,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                      color: \"error\",\n                      size: \"small\",\n                      onClick: () => handleDelete(row.id),\n                      sx: {\n                        bgcolor: '#fef2f2',\n                        '&:hover': {\n                          bgcolor: '#fecaca'\n                        },\n                        width: 32,\n                        height: 32,\n                        borderRadius: 1.5\n                      },\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                        sx: {\n                          fontSize: 16\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 656,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 644,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 615,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 25\n                }, this)]\n              }, row.id || idx, true, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 23\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 md:px-6 py-4 bg-gray-50 border-t border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-700\",\n              children: [\"Menampilkan \", page * rowsPerPage + 1, \"-\", Math.min((page + 1) * rowsPerPage, filteredRows.length), \" dari \", filteredRows.length, \" berita\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 673,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 672,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setPage(Math.max(0, page - 1)),\n              disabled: page === 0,\n              className: \"px-4 py-2 text-sm border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 transition-colors font-medium\",\n              children: \"Sebelumnya\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 678,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setPage(page + 1),\n              disabled: (page + 1) * rowsPerPage >= filteredRows.length,\n              className: \"px-4 py-2 text-sm border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 transition-colors font-medium\",\n              children: \"Selanjutnya\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 677,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 671,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 670,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 452,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 314,\n    columnNumber: 5\n  }, this);\n}\n_s2(NewsPage, \"Dgk1L/jJ07bvcDWTg9qCTrXJ31U=\", false, function () {\n  return [useNavigate, useTheme, useMediaQuery];\n});\n_c3 = NewsPage;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"DeskripsiCell\");\n$RefreshReg$(_c2, \"MobileNewsCard\");\n$RefreshReg$(_c3, \"NewsPage\");", "map": {"version": 3, "names": ["<PERSON><PERSON>", "AddIcon", "useNavigate", "React", "useEffect", "useState", "useMemo", "Box", "Breadcrumbs", "Link", "Typography", "useMediaQuery", "useTheme", "Avatar", "IconButton", "EditIcon", "DeleteIcon", "VisibilityIcon", "<PERSON><PERSON>", "Checkbox", "SearchIcon", "FilterListIcon", "MoreVertIcon", "Chip", "jsxDEV", "_jsxDEV", "DeskripsiCell", "text", "max<PERSON><PERSON><PERSON>", "isMobile", "_s", "expanded", "setExpanded", "isLong", "length", "className", "children", "slice", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "MobileNewsCard", "row", "idx", "checked", "handleClick", "handleEdit", "handleDelete", "navigate", "page", "rowsPerPage", "formatDate", "raw", "d", "Date", "isNaN", "bulan", "tgl", "getDate", "bln", "getMonth", "thn", "getFullYear", "jam", "getHours", "toString", "padStart", "menit", "getMinutes", "color", "onChange", "id", "inputProps", "sx", "label", "size", "bgcolor", "fontWeight", "fontSize", "date", "created_at", "alt", "title", "src", "image", "startsWith", "width", "height", "borderRadius", "border", "boxShadow", "variant", "description", "_c2", "NewsPage", "_s2", "rows", "setRows", "setPage", "setRowsPerPage", "selected", "setSelected", "searchTerm", "setSearchTerm", "theme", "breakpoints", "down", "fetch", "then", "res", "json", "data", "Array", "isArray", "catch", "filteredRows", "filter", "_row$title", "_row$description", "toLowerCase", "includes", "emptyRows", "Math", "max", "visibleRows", "isSelected", "indexOf", "handleSelectAllClick", "event", "target", "newSelected", "map", "Set", "some", "selectedIndex", "concat", "selId", "handleDeleteSelected", "confirm", "fire", "icon", "showCancelButton", "confirmButtonText", "cancelButtonText", "confirmButtonColor", "cancelButtonColor", "isConfirmed", "token", "localStorage", "getItem", "method", "headers", "timer", "showConfirmButton", "xs", "md", "underline", "href", "py", "px", "startIcon", "textTransform", "position", "left", "top", "transform", "type", "placeholder", "value", "e", "parseInt", "option", "indeterminate", "mb", "colSpan", "min", "disabled", "_c3", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/pages/admin/pages/NewsPage.js"], "sourcesContent": ["import Button from '@mui/material/Button';\r\nimport AddIcon from '@mui/icons-material/Add';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport React, { useEffect, useState, useMemo } from 'react';\r\nimport Box from '@mui/material/Box';\r\nimport Breadcrumbs from '@mui/material/Breadcrumbs';\r\nimport Link from '@mui/material/Link';\r\nimport Typography from '@mui/material/Typography';\r\nimport useMediaQuery from '@mui/material/useMediaQuery';\r\nimport { useTheme } from '@mui/material/styles';\r\nimport Avatar from '@mui/material/Avatar';\r\nimport IconButton from '@mui/material/IconButton';\r\nimport EditIcon from '@mui/icons-material/Edit';\r\nimport DeleteIcon from '@mui/icons-material/Delete';\r\nimport VisibilityIcon from '@mui/icons-material/Visibility';\r\nimport Swal from 'sweetalert2';\r\nimport Checkbox from '@mui/material/Checkbox';\r\nimport SearchIcon from '@mui/icons-material/Search';\r\nimport FilterListIcon from '@mui/icons-material/FilterList';\r\nimport MoreVertIcon from '@mui/icons-material/MoreVert';\r\nimport Chip from '@mui/material/Chip';\r\n\r\nfunction DeskripsiCell({ text, maxLength, isMobile }) {\r\n  const [expanded, setExpanded] = useState(false);\r\n  if (!text) return '';\r\n  const isLong = text.length > maxLength;\r\n  return (\r\n    <div className=\"flex flex-col gap-1\">\r\n      <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-700 leading-tight ${expanded ? 'whitespace-normal' : 'truncate'}`}>\r\n        {expanded || !isLong ? text : text.slice(0, maxLength) + '...'}\r\n      </p>\r\n      {isLong && (\r\n        <button \r\n          className={`text-blue-600 hover:text-blue-800 text-xs font-medium self-start transition-colors`}\r\n          onClick={() => setExpanded(!expanded)}\r\n        >\r\n          {expanded ? 'Tutup' : 'Baca'}\r\n        </button>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\n// Mobile Card Component\r\nfunction MobileNewsCard({ row, idx, checked, handleClick, handleEdit, handleDelete, navigate, page, rowsPerPage, isMobile }) {\r\n  const formatDate = (raw) => {\r\n    if (!raw) return '';\r\n    const d = new Date(raw);\r\n    if (isNaN(d)) return raw;\r\n    const bulan = [\r\n      'Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun',\r\n      'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'\r\n    ];\r\n    const tgl = d.getDate();\r\n    const bln = bulan[d.getMonth()];\r\n    const thn = d.getFullYear();\r\n    const jam = d.getHours().toString().padStart(2, '0');\r\n    const menit = d.getMinutes().toString().padStart(2, '0');\r\n    return `${tgl}/${bln} ${jam}:${menit}`;\r\n  };\r\n\r\n  return (\r\n    <div className={`bg-white border border-gray-200 rounded-lg p-4 mb-3 transition-all duration-200 ${checked ? 'ring-2 ring-blue-500 bg-blue-50 shadow-lg' : 'hover:bg-gray-50 hover:shadow-md'}`}>\r\n      {/* Header with checkbox and number */}\r\n      <div className=\"flex items-center justify-between mb-3\">\r\n        <div className=\"flex items-center gap-3\">\r\n          <Checkbox\r\n            color=\"primary\"\r\n            checked={checked}\r\n            onChange={() => handleClick(row.id)}\r\n            inputProps={{ 'aria-label': `select row ${idx + 1}` }}\r\n            sx={{\r\n              '&.Mui-checked': {\r\n                color: '#2563eb',\r\n              },\r\n            }}\r\n          />\r\n          <Chip \r\n            label={`#${page * rowsPerPage + idx + 1}`}\r\n            size=\"small\"\r\n            sx={{ \r\n              bgcolor: '#f1f5f9', \r\n              color: '#475569',\r\n              fontWeight: 600,\r\n              fontSize: '0.75rem'\r\n            }}\r\n          />\r\n        </div>\r\n        <span className=\"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full\">\r\n          {formatDate(row.date || row.created_at)}\r\n        </span>\r\n      </div>\r\n\r\n      {/* Content */}\r\n      <div className=\"flex gap-3\">\r\n        {/* Image */}\r\n        <div className=\"flex-shrink-0\">\r\n          <Avatar\r\n            alt={row.title}\r\n            src={row.image ? (row.image.startsWith('/uploads/') ? row.image : `/uploads/${row.image}`) : ''}\r\n            sx={{ \r\n              width: 60, \r\n              height: 60, \r\n              borderRadius: 2,\r\n              border: '2px solid #e2e8f0',\r\n              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\r\n            }}\r\n            variant=\"square\"\r\n          />\r\n        </div>\r\n\r\n        {/* Text Content */}\r\n        <div className=\"flex-1 min-w-0\">\r\n          <h3 className=\"font-semibold text-gray-900 text-sm mb-1 truncate\">\r\n            {row.title}\r\n          </h3>\r\n          <p className=\"text-xs text-gray-600 mb-3 line-clamp-2\">\r\n            {row.description}\r\n          </p>\r\n          \r\n          {/* Action Buttons */}\r\n          <div className=\"flex gap-2\">\r\n            <button\r\n              onClick={() => navigate(`/admin/pages/previewnews/${row.id}`)}\r\n              className=\"flex items-center gap-1 px-3 py-1.5 text-xs bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-all duration-200 font-medium\"\r\n            >\r\n              <VisibilityIcon sx={{ fontSize: 14 }} />\r\n              Lihat\r\n            </button>\r\n            <button\r\n              onClick={() => handleEdit(row.id)}\r\n              className=\"flex items-center gap-1 px-3 py-1.5 text-xs bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-all duration-200 font-medium\"\r\n            >\r\n              <EditIcon sx={{ fontSize: 14 }} />\r\n              Edit\r\n            </button>\r\n            <button\r\n              onClick={() => handleDelete(row.id)}\r\n              className=\"flex items-center gap-1 px-3 py-1.5 text-xs bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-all duration-200 font-medium\"\r\n            >\r\n              <DeleteIcon sx={{ fontSize: 14 }} />\r\n              Hapus\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default function NewsPage() {\r\n  const [rows, setRows] = useState([]);\r\n  const [page, setPage] = useState(0);\r\n  const [rowsPerPage, setRowsPerPage] = useState(10);\r\n  const [selected, setSelected] = useState([]);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const navigate = useNavigate();\r\n  const theme = useTheme();\r\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\r\n\r\n  useEffect(() => {\r\n    fetch('/api/posts')\r\n      .then(res => res.json())\r\n      .then(data => {\r\n        if (Array.isArray(data)) {\r\n          setRows(data);\r\n        } else {\r\n          setRows([]);\r\n        }\r\n      })\r\n      .catch(() => setRows([]));\r\n  }, []);\r\n\r\n  // Filter rows based on search term\r\n  const filteredRows = useMemo(() => {\r\n    if (!searchTerm) return rows;\r\n    return rows.filter(row => \r\n      row.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n      row.description?.toLowerCase().includes(searchTerm.toLowerCase())\r\n    );\r\n  }, [rows, searchTerm]);\r\n\r\n  const emptyRows = page > 0 ? Math.max(0, (1 + page) * rowsPerPage - filteredRows.length) : 0;\r\n  const visibleRows = useMemo(\r\n    () => filteredRows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage),\r\n    [filteredRows, page, rowsPerPage]\r\n  );\r\n\r\n  // Checkbox logic\r\n  const isSelected = (id) => selected.indexOf(id) !== -1;\r\n  const handleSelectAllClick = (event) => {\r\n    if (event.target.checked) {\r\n      const newSelected = visibleRows.map((row) => row.id);\r\n      setSelected([...new Set([...selected, ...newSelected])]);\r\n    } else {\r\n      const newSelected = selected.filter(id => !visibleRows.some(row => row.id === id));\r\n      setSelected(newSelected);\r\n    }\r\n  };\r\n  const handleClick = (id) => {\r\n    const selectedIndex = selected.indexOf(id);\r\n    let newSelected = [];\r\n    if (selectedIndex === -1) {\r\n      newSelected = newSelected.concat(selected, id);\r\n    } else {\r\n      newSelected = selected.filter(selId => selId !== id);\r\n    }\r\n    setSelected(newSelected);\r\n  };\r\n\r\n  const handleDeleteSelected = async () => {\r\n    if (selected.length === 0) return;\r\n    const confirm = await Swal.fire({\r\n      title: 'Hapus Berita?',\r\n      text: `Yakin ingin menghapus ${selected.length} berita terpilih?`,\r\n      icon: 'warning',\r\n      showCancelButton: true,\r\n      confirmButtonText: 'Ya, hapus',\r\n      cancelButtonText: 'Batal',\r\n      confirmButtonColor: '#dc2626',\r\n      cancelButtonColor: '#6b7280',\r\n    });\r\n    if (confirm.isConfirmed) {\r\n      const token = localStorage.getItem('jwt');\r\n      for (const id of selected) {\r\n        await fetch(`/api/posts/${id}`, {\r\n          method: 'DELETE',\r\n          headers: { 'Authorization': `Bearer ${token}` }\r\n        });\r\n      }\r\n      setSelected([]);\r\n      // Refresh data\r\n      fetch('/api/posts')\r\n        .then(res => res.json())\r\n        .then(data => {\r\n          if (Array.isArray(data)) {\r\n            setRows(data);\r\n          } else {\r\n            setRows([]);\r\n          }\r\n        })\r\n        .catch(() => setRows([]));\r\n      \r\n      Swal.fire({\r\n        title: 'Berhasil!',\r\n        text: `${selected.length} berita berhasil dihapus`,\r\n        icon: 'success',\r\n        timer: 2000,\r\n        showConfirmButton: false\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleEdit = (id) => {\r\n    navigate(`/admin/pages/editnews/${id}`);\r\n  };\r\n\r\n  const handleDelete = async (id) => {\r\n    const confirm = await Swal.fire({\r\n      title: 'Hapus Berita?',\r\n      text: 'Yakin ingin menghapus berita ini?',\r\n      icon: 'warning',\r\n      showCancelButton: true,\r\n      confirmButtonText: 'Ya, hapus',\r\n      cancelButtonText: 'Batal',\r\n      confirmButtonColor: '#dc2626',\r\n      cancelButtonColor: '#6b7280',\r\n    });\r\n    if (confirm.isConfirmed) {\r\n      const token = localStorage.getItem('jwt');\r\n      await fetch(`/api/posts/${id}`, {\r\n        method: 'DELETE',\r\n        headers: { 'Authorization': `Bearer ${token}` }\r\n      });\r\n      // Refresh data\r\n      fetch('/api/posts')\r\n        .then(res => res.json())\r\n        .then(data => {\r\n          if (Array.isArray(data)) {\r\n            setRows(data);\r\n          } else {\r\n            setRows([]);\r\n          }\r\n        })\r\n        .catch(() => setRows([]));\r\n      \r\n      Swal.fire({\r\n        title: 'Berhasil!',\r\n        text: 'Berita berhasil dihapus',\r\n        icon: 'success',\r\n        timer: 2000,\r\n        showConfirmButton: false\r\n      });\r\n    }\r\n  };\r\n\r\n  const formatDate = (raw) => {\r\n    if (!raw) return '';\r\n    const d = new Date(raw);\r\n    if (isNaN(d)) return raw;\r\n    const bulan = [\r\n      'Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun',\r\n      'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'\r\n    ];\r\n    const tgl = d.getDate();\r\n    const bln = bulan[d.getMonth()];\r\n    const thn = d.getFullYear();\r\n    const jam = d.getHours().toString().padStart(2, '0');\r\n    const menit = d.getMinutes().toString().padStart(2, '0');\r\n    return `${tgl}/${bln}/${thn} ${jam}:${menit}`;\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full flex flex-col gap-4\">\r\n      {/* Breadcrumb */}\r\n      <div className=\"p-4 md:p-6 bg-white rounded-lg border border-gray-200 shadow-sm\">\r\n        <Breadcrumbs aria-label=\"breadcrumb\" sx={{ fontSize: { xs: '12px', md: '14px' } }}>\r\n          <Link underline=\"hover\" color=\"inherit\" href=\"/admin/dashboard\">\r\n            Dashboard\r\n          </Link>\r\n          <Typography color=\"text.primary\">Berita</Typography>\r\n        </Breadcrumbs>\r\n      </div>\r\n\r\n      {/* Header Actions */}\r\n      <div className=\"bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden\">\r\n        <div className=\"p-4 md:p-6 border-b border-gray-200\">\r\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\r\n            <div>\r\n              <h2 className=\"text-xl md:text-2xl font-bold text-gray-800 mb-1\">\r\n                Daftar Berita\r\n              </h2>\r\n              <p className=\"text-gray-600 text-sm\">\r\n                Kelola dan atur semua berita dalam sistem\r\n              </p>\r\n            </div>\r\n            <div className=\"flex flex-col sm:flex-row gap-3\">\r\n              {selected.length > 0 && (\r\n                <Button\r\n                  variant=\"outlined\"\r\n                  color=\"error\"\r\n                  onClick={handleDeleteSelected}\r\n                  sx={{ \r\n                    fontWeight: 600, \r\n                    borderRadius: 2,\r\n                    fontSize: { xs: '12px', md: '14px' },\r\n                    py: { xs: 1, md: 1.5 },\r\n                    px: { xs: 2, md: 3 }\r\n                  }}\r\n                >\r\n                  Hapus {selected.length} Terpilih\r\n                </Button>\r\n              )}\r\n              <Button\r\n                variant=\"contained\"\r\n                color=\"primary\"\r\n                startIcon={<AddIcon />}\r\n                sx={{ \r\n                  borderRadius: 2, \r\n                  textTransform: 'none', \r\n                  fontWeight: 600,\r\n                  fontSize: { xs: '12px', md: '14px' },\r\n                  py: { xs: 1, md: 1.5 },\r\n                  px: { xs: 2, md: 3 },\r\n                  boxShadow: '0 4px 6px -1px rgba(37, 99, 235, 0.2)',\r\n                  '&:hover': {\r\n                    boxShadow: '0 6px 8px -1px rgba(37, 99, 235, 0.3)',\r\n                  }\r\n                }}\r\n                onClick={() => navigate('/admin/pages/tambahberita')}\r\n              >\r\n                Tambah Berita\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Search and Filter Bar */}\r\n        <div className=\"p-4 md:p-6 bg-gray-50 border-b border-gray-200\">\r\n          <div className=\"flex flex-col sm:flex-row gap-3\">\r\n            <div className=\"flex-1 relative\">\r\n              <SearchIcon sx={{ \r\n                position: 'absolute', \r\n                left: 12, \r\n                top: '50%', \r\n                transform: 'translateY(-50%)', \r\n                color: '#9ca3af',\r\n                fontSize: 20\r\n              }} />\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Cari berita berdasarkan judul atau deskripsi...\"\r\n                value={searchTerm}\r\n                onChange={(e) => setSearchTerm(e.target.value)}\r\n                className=\"w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm\"\r\n              />\r\n            </div>\r\n            <Button\r\n              variant=\"outlined\"\r\n              startIcon={<FilterListIcon />}\r\n              sx={{ \r\n                borderRadius: 2,\r\n                textTransform: 'none',\r\n                fontWeight: 500,\r\n                fontSize: { xs: '12px', md: '14px' },\r\n                py: { xs: 1, md: 1.5 },\r\n                px: { xs: 2, md: 3 }\r\n              }}\r\n            >\r\n              Filter\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Results Summary */}\r\n        <div className=\"px-4 md:px-6 py-3 bg-white border-b border-gray-200\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <Typography sx={{ \r\n                color: '#6b7280', \r\n                fontSize: { xs: '12px', md: '14px' },\r\n                fontWeight: 500\r\n              }}>\r\n                {filteredRows.length} berita ditemukan\r\n              </Typography>\r\n              {selected.length > 0 && (\r\n                <Chip \r\n                  label={`${selected.length} terpilih`}\r\n                  size=\"small\"\r\n                  color=\"primary\"\r\n                  sx={{ fontSize: '0.75rem' }}\r\n                />\r\n              )}\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <span className=\"text-sm text-gray-600\">Baris per halaman:</span>\r\n              <select \r\n                value={rowsPerPage} \r\n                onChange={(e) => { setRowsPerPage(parseInt(e.target.value, 10)); setPage(0); }}\r\n                className=\"border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n              >\r\n                {[5, 10, 25, 50].map((option) => (\r\n                  <option key={option} value={option}>{option}</option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Content - Mobile Cards or Desktop Table */}\r\n      <div className=\"bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden\">\r\n        {isMobile ? (\r\n          /* Mobile Card View */\r\n          <div className=\"p-4\">\r\n            {/* Mobile Select All Header */}\r\n            <div className=\"flex items-center justify-between mb-4 p-3 bg-gray-50 rounded-lg\">\r\n              <div className=\"flex items-center gap-3\">\r\n                <Checkbox\r\n                  color=\"primary\"\r\n                  indeterminate={visibleRows.filter(row => isSelected(row.id)).length > 0 && visibleRows.filter(row => isSelected(row.id)).length < visibleRows.length}\r\n                  checked={visibleRows.length > 0 && visibleRows.filter(row => isSelected(row.id)).length === visibleRows.length}\r\n                  onChange={handleSelectAllClick}\r\n                  inputProps={{ 'aria-label': 'select all news' }}\r\n                  sx={{\r\n                    '&.Mui-checked': {\r\n                      color: '#2563eb',\r\n                    },\r\n                  }}\r\n                />\r\n                <span className=\"text-sm font-medium text-gray-700\">\r\n                  Pilih Semua\r\n                </span>\r\n              </div>\r\n              {selected.length > 0 && (\r\n                <Chip\r\n                  label={`${selected.length} terpilih`}\r\n                  size=\"small\"\r\n                  color=\"primary\"\r\n                  sx={{ fontSize: '0.75rem' }}\r\n                />\r\n              )}\r\n            </div>\r\n\r\n            {/* Mobile Cards */}\r\n            {visibleRows.length === 0 ? (\r\n              <div className=\"p-12 text-center\">\r\n                <div className=\"text-gray-500\">\r\n                  <SearchIcon sx={{ fontSize: 48, color: '#d1d5db', mb: 2 }} />\r\n                  <p className=\"text-lg font-medium mb-2\">\r\n                    {searchTerm ? 'Tidak ada hasil pencarian' : 'Belum ada berita'}\r\n                  </p>\r\n                  <p className=\"text-sm\">\r\n                    {searchTerm ? 'Coba ubah kata kunci pencarian' : 'Berita akan muncul di sini setelah ditambahkan'}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              visibleRows.map((row, idx) => {\r\n                const checked = isSelected(row.id);\r\n                return (\r\n                  <MobileNewsCard\r\n                    key={row.id || idx}\r\n                    row={row}\r\n                    idx={idx}\r\n                    checked={checked}\r\n                    handleClick={handleClick}\r\n                    handleEdit={handleEdit}\r\n                    handleDelete={handleDelete}\r\n                    navigate={navigate}\r\n                    page={page}\r\n                    rowsPerPage={rowsPerPage}\r\n                    isMobile={isMobile}\r\n                  />\r\n                );\r\n              })\r\n            )}\r\n          </div>\r\n        ) : (\r\n          /* Desktop Table View */\r\n          <div className=\"overflow-x-auto\">\r\n            <table className=\"w-full\">\r\n              <thead className=\"bg-gradient-to-r from-gray-800 to-gray-900 text-white\">\r\n                <tr>\r\n                  <th className=\"w-16 p-4 text-center\">\r\n                    <Checkbox\r\n                      color=\"primary\"\r\n                      indeterminate={visibleRows.filter(row => isSelected(row.id)).length > 0 && visibleRows.filter(row => isSelected(row.id)).length < visibleRows.length}\r\n                      checked={visibleRows.length > 0 && visibleRows.filter(row => isSelected(row.id)).length === visibleRows.length}\r\n                      onChange={handleSelectAllClick}\r\n                      inputProps={{ 'aria-label': 'select all news' }}\r\n                      sx={{\r\n                        color: '#fff',\r\n                        '&.Mui-checked': { color: '#fff' },\r\n                        '&.MuiCheckbox-indeterminate': { color: '#fff' }\r\n                      }}\r\n                    />\r\n                  </th>\r\n                  <th className=\"w-20 p-4 text-center font-semibold text-sm\">No</th>\r\n                  <th className=\"w-24 p-4 text-left font-semibold text-sm\">Gambar</th>\r\n                  <th className=\"min-w-[200px] p-4 text-left font-semibold text-sm\">Judul</th>\r\n                  <th className=\"min-w-[300px] p-4 text-left font-semibold text-sm\">Deskripsi</th>\r\n                  <th className=\"w-40 p-4 text-left font-semibold text-sm\">Tgl & Waktu</th>\r\n                  <th className=\"w-32 p-4 text-center font-semibold text-sm\">Aksi</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody className=\"bg-white\">\r\n                {visibleRows.length === 0 ? (\r\n                  <tr>\r\n                    <td colSpan=\"7\" className=\"p-12 text-center\">\r\n                      <div className=\"text-gray-500\">\r\n                        <SearchIcon sx={{ fontSize: 48, color: '#d1d5db', mb: 2 }} />\r\n                        <p className=\"text-lg font-medium mb-2\">\r\n                          {searchTerm ? 'Tidak ada hasil pencarian' : 'Belum ada berita'}\r\n                        </p>\r\n                        <p className=\"text-sm\">\r\n                          {searchTerm ? 'Coba ubah kata kunci pencarian' : 'Berita akan muncul di sini setelah ditambahkan'}\r\n                        </p>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                ) : (\r\n                  visibleRows.map((row, idx) => {\r\n                    const checked = isSelected(row.id);\r\n                    return (\r\n                      <tr key={row.id || idx} className={`border-b border-gray-100 transition-colors duration-200 ${checked ? 'bg-blue-50' : 'hover:bg-gray-50'}`}>\r\n                        <td className=\"w-16 p-4 text-center\">\r\n                          <Checkbox\r\n                            color=\"primary\"\r\n                            checked={checked}\r\n                            onChange={() => handleClick(row.id)}\r\n                            inputProps={{ 'aria-label': `select row ${idx + 1}` }}\r\n                          />\r\n                        </td>\r\n                        <td className=\"w-20 p-4 text-center\">\r\n                          <Chip\r\n                            label={page * rowsPerPage + idx + 1}\r\n                            size=\"small\"\r\n                            sx={{\r\n                              bgcolor: '#f1f5f9',\r\n                              color: '#475569',\r\n                              fontWeight: 600,\r\n                              fontSize: '0.75rem'\r\n                            }}\r\n                          />\r\n                        </td>\r\n                        <td className=\"w-24 p-4\">\r\n                          <Avatar\r\n                            alt={row.title}\r\n                            src={row.image ? (row.image.startsWith('/uploads/') ? row.image : `/uploads/${row.image}`) : ''}\r\n                            sx={{\r\n                              width: 48,\r\n                              height: 48,\r\n                              borderRadius: 2,\r\n                              border: '2px solid #e2e8f0',\r\n                              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\r\n                            }}\r\n                            variant=\"square\"\r\n                          />\r\n                        </td>\r\n                        <td className=\"min-w-[200px] p-4\">\r\n                          <p className=\"text-sm font-semibold text-gray-900 break-words\">\r\n                            {row.title}\r\n                          </p>\r\n                        </td>\r\n                        <td className=\"min-w-[300px] p-4\">\r\n                          <DeskripsiCell text={row.description} maxLength={100} isMobile={isMobile} />\r\n                        </td>\r\n                        <td className=\"w-40 p-4\">\r\n                          <p className=\"text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded-full inline-block whitespace-nowrap\">\r\n                            {formatDate(row.date || row.created_at)}\r\n                          </p>\r\n                        </td>\r\n                        <td className=\"w-32 p-4\">\r\n                          <div className=\"flex justify-center gap-1\">\r\n                            <IconButton\r\n                              color=\"info\"\r\n                              size=\"small\"\r\n                              onClick={() => navigate(`/admin/pages/previewnews/${row.id}`)}\r\n                              sx={{\r\n                                bgcolor: '#eff6ff',\r\n                                '&:hover': { bgcolor: '#dbeafe' },\r\n                                width: 32,\r\n                                height: 32,\r\n                                borderRadius: 1.5\r\n                              }}\r\n                            >\r\n                              <VisibilityIcon sx={{ fontSize: 16 }} />\r\n                            </IconButton>\r\n                            <IconButton\r\n                              color=\"primary\"\r\n                              size=\"small\"\r\n                              onClick={() => handleEdit(row.id)}\r\n                              sx={{\r\n                                bgcolor: '#f0fdf4',\r\n                                '&:hover': { bgcolor: '#dcfce7' },\r\n                                width: 32,\r\n                                height: 32,\r\n                                borderRadius: 1.5\r\n                              }}\r\n                            >\r\n                              <EditIcon sx={{ fontSize: 16 }} />\r\n                            </IconButton>\r\n                            <IconButton\r\n                              color=\"error\"\r\n                              size=\"small\"\r\n                              onClick={() => handleDelete(row.id)}\r\n                              sx={{\r\n                                bgcolor: '#fef2f2',\r\n                                '&:hover': { bgcolor: '#fecaca' },\r\n                                width: 32,\r\n                                height: 32,\r\n                                borderRadius: 1.5\r\n                              }}\r\n                            >\r\n                              <DeleteIcon sx={{ fontSize: 16 }} />\r\n                            </IconButton>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                    );\r\n                  })\r\n                )}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        )}\r\n\r\n        {/* Pagination */}\r\n        <div className=\"px-4 md:px-6 py-4 bg-gray-50 border-t border-gray-200\">\r\n          <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <span className=\"text-sm text-gray-700\">\r\n                Menampilkan {page * rowsPerPage + 1}-{Math.min((page + 1) * rowsPerPage, filteredRows.length)} dari {filteredRows.length} berita\r\n              </span>\r\n            </div>\r\n            <div className=\"flex gap-2\">\r\n              <button\r\n                onClick={() => setPage(Math.max(0, page - 1))}\r\n                disabled={page === 0}\r\n                className=\"px-4 py-2 text-sm border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 transition-colors font-medium\"\r\n              >\r\n                Sebelumnya\r\n              </button>\r\n              <button\r\n                onClick={() => setPage(page + 1)}\r\n                disabled={(page + 1) * rowsPerPage >= filteredRows.length}\r\n                className=\"px-4 py-2 text-sm border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 transition-colors font-medium\"\r\n              >\r\n                Selanjutnya\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;;AAAA,OAAOA,MAAM,MAAM,sBAAsB;AACzC,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AAC3D,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,IAAI,MAAM,aAAa;AAC9B,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,IAAI,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,SAASC,aAAaA,CAAC;EAAEC,IAAI;EAAEC,SAAS;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EACpD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC/C,IAAI,CAACsB,IAAI,EAAE,OAAO,EAAE;EACpB,MAAMM,MAAM,GAAGN,IAAI,CAACO,MAAM,GAAGN,SAAS;EACtC,oBACEH,OAAA;IAAKU,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClCX,OAAA;MAAGU,SAAS,EAAE,GAAGN,QAAQ,GAAG,SAAS,GAAG,SAAS,gCAAgCE,QAAQ,GAAG,mBAAmB,GAAG,UAAU,EAAG;MAAAK,QAAA,EAC5HL,QAAQ,IAAI,CAACE,MAAM,GAAGN,IAAI,GAAGA,IAAI,CAACU,KAAK,CAAC,CAAC,EAAET,SAAS,CAAC,GAAG;IAAK;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC,EACHR,MAAM,iBACLR,OAAA;MACEU,SAAS,EAAE,oFAAqF;MAChGO,OAAO,EAAEA,CAAA,KAAMV,WAAW,CAAC,CAACD,QAAQ,CAAE;MAAAK,QAAA,EAErCL,QAAQ,GAAG,OAAO,GAAG;IAAM;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CACT;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;;AAEA;AAAAX,EAAA,CArBSJ,aAAa;AAAAiB,EAAA,GAAbjB,aAAa;AAsBtB,SAASkB,cAAcA,CAAC;EAAEC,GAAG;EAAEC,GAAG;EAAEC,OAAO;EAAEC,WAAW;EAAEC,UAAU;EAAEC,YAAY;EAAEC,QAAQ;EAAEC,IAAI;EAAEC,WAAW;EAAExB;AAAS,CAAC,EAAE;EAC3H,MAAMyB,UAAU,GAAIC,GAAG,IAAK;IAC1B,IAAI,CAACA,GAAG,EAAE,OAAO,EAAE;IACnB,MAAMC,CAAC,GAAG,IAAIC,IAAI,CAACF,GAAG,CAAC;IACvB,IAAIG,KAAK,CAACF,CAAC,CAAC,EAAE,OAAOD,GAAG;IACxB,MAAMI,KAAK,GAAG,CACZ,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACxC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CACzC;IACD,MAAMC,GAAG,GAAGJ,CAAC,CAACK,OAAO,CAAC,CAAC;IACvB,MAAMC,GAAG,GAAGH,KAAK,CAACH,CAAC,CAACO,QAAQ,CAAC,CAAC,CAAC;IAC/B,MAAMC,GAAG,GAAGR,CAAC,CAACS,WAAW,CAAC,CAAC;IAC3B,MAAMC,GAAG,GAAGV,CAAC,CAACW,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACpD,MAAMC,KAAK,GAAGd,CAAC,CAACe,UAAU,CAAC,CAAC,CAACH,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACxD,OAAO,GAAGT,GAAG,IAAIE,GAAG,IAAII,GAAG,IAAII,KAAK,EAAE;EACxC,CAAC;EAED,oBACE7C,OAAA;IAAKU,SAAS,EAAE,mFAAmFY,OAAO,GAAG,2CAA2C,GAAG,kCAAkC,EAAG;IAAAX,QAAA,gBAE9LX,OAAA;MAAKU,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDX,OAAA;QAAKU,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtCX,OAAA,CAACN,QAAQ;UACPqD,KAAK,EAAC,SAAS;UACfzB,OAAO,EAAEA,OAAQ;UACjB0B,QAAQ,EAAEA,CAAA,KAAMzB,WAAW,CAACH,GAAG,CAAC6B,EAAE,CAAE;UACpCC,UAAU,EAAE;YAAE,YAAY,EAAE,cAAc7B,GAAG,GAAG,CAAC;UAAG,CAAE;UACtD8B,EAAE,EAAE;YACF,eAAe,EAAE;cACfJ,KAAK,EAAE;YACT;UACF;QAAE;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFhB,OAAA,CAACF,IAAI;UACHsD,KAAK,EAAE,IAAIzB,IAAI,GAAGC,WAAW,GAAGP,GAAG,GAAG,CAAC,EAAG;UAC1CgC,IAAI,EAAC,OAAO;UACZF,EAAE,EAAE;YACFG,OAAO,EAAE,SAAS;YAClBP,KAAK,EAAE,SAAS;YAChBQ,UAAU,EAAE,GAAG;YACfC,QAAQ,EAAE;UACZ;QAAE;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNhB,OAAA;QAAMU,SAAS,EAAC,0DAA0D;QAAAC,QAAA,EACvEkB,UAAU,CAACT,GAAG,CAACqC,IAAI,IAAIrC,GAAG,CAACsC,UAAU;MAAC;QAAA7C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNhB,OAAA;MAAKU,SAAS,EAAC,YAAY;MAAAC,QAAA,gBAEzBX,OAAA;QAAKU,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BX,OAAA,CAACZ,MAAM;UACLuE,GAAG,EAAEvC,GAAG,CAACwC,KAAM;UACfC,GAAG,EAAEzC,GAAG,CAAC0C,KAAK,GAAI1C,GAAG,CAAC0C,KAAK,CAACC,UAAU,CAAC,WAAW,CAAC,GAAG3C,GAAG,CAAC0C,KAAK,GAAG,YAAY1C,GAAG,CAAC0C,KAAK,EAAE,GAAI,EAAG;UAChGX,EAAE,EAAE;YACFa,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVC,YAAY,EAAE,CAAC;YACfC,MAAM,EAAE,mBAAmB;YAC3BC,SAAS,EAAE;UACb,CAAE;UACFC,OAAO,EAAC;QAAQ;UAAAxD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNhB,OAAA;QAAKU,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BX,OAAA;UAAIU,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAC9DS,GAAG,CAACwC;QAAK;UAAA/C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eACLhB,OAAA;UAAGU,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EACnDS,GAAG,CAACkD;QAAW;UAAAzD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eAGJhB,OAAA;UAAKU,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBX,OAAA;YACEiB,OAAO,EAAEA,CAAA,KAAMS,QAAQ,CAAC,4BAA4BN,GAAG,CAAC6B,EAAE,EAAE,CAAE;YAC9DvC,SAAS,EAAC,4IAA4I;YAAAC,QAAA,gBAEtJX,OAAA,CAACR,cAAc;cAAC2D,EAAE,EAAE;gBAAEK,QAAQ,EAAE;cAAG;YAAE;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,SAE1C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThB,OAAA;YACEiB,OAAO,EAAEA,CAAA,KAAMO,UAAU,CAACJ,GAAG,CAAC6B,EAAE,CAAE;YAClCvC,SAAS,EAAC,+IAA+I;YAAAC,QAAA,gBAEzJX,OAAA,CAACV,QAAQ;cAAC6D,EAAE,EAAE;gBAAEK,QAAQ,EAAE;cAAG;YAAE;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,QAEpC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThB,OAAA;YACEiB,OAAO,EAAEA,CAAA,KAAMQ,YAAY,CAACL,GAAG,CAAC6B,EAAE,CAAE;YACpCvC,SAAS,EAAC,yIAAyI;YAAAC,QAAA,gBAEnJX,OAAA,CAACT,UAAU;cAAC4D,EAAE,EAAE;gBAAEK,QAAQ,EAAE;cAAG;YAAE;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,SAEtC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACuD,GAAA,GAxGQpD,cAAc;AA0GvB,eAAe,SAASqD,QAAQA,CAAA,EAAG;EAAAC,GAAA;EACjC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC+C,IAAI,EAAEiD,OAAO,CAAC,GAAGhG,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACgD,WAAW,EAAEiD,cAAc,CAAC,GAAGjG,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACkG,QAAQ,EAAEC,WAAW,CAAC,GAAGnG,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoG,UAAU,EAAEC,aAAa,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM8C,QAAQ,GAAGjD,WAAW,CAAC,CAAC;EAC9B,MAAMyG,KAAK,GAAG/F,QAAQ,CAAC,CAAC;EACxB,MAAMiB,QAAQ,GAAGlB,aAAa,CAACgG,KAAK,CAACC,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAE5DzG,SAAS,CAAC,MAAM;IACd0G,KAAK,CAAC,YAAY,CAAC,CAChBC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACG,IAAI,IAAI;MACZ,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;QACvBd,OAAO,CAACc,IAAI,CAAC;MACf,CAAC,MAAM;QACLd,OAAO,CAAC,EAAE,CAAC;MACb;IACF,CAAC,CAAC,CACDiB,KAAK,CAAC,MAAMjB,OAAO,CAAC,EAAE,CAAC,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkB,YAAY,GAAGhH,OAAO,CAAC,MAAM;IACjC,IAAI,CAACmG,UAAU,EAAE,OAAON,IAAI;IAC5B,OAAOA,IAAI,CAACoB,MAAM,CAAC1E,GAAG;MAAA,IAAA2E,UAAA,EAAAC,gBAAA;MAAA,OACpB,EAAAD,UAAA,GAAA3E,GAAG,CAACwC,KAAK,cAAAmC,UAAA,uBAATA,UAAA,CAAWE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClB,UAAU,CAACiB,WAAW,CAAC,CAAC,CAAC,OAAAD,gBAAA,GAC3D5E,GAAG,CAACkD,WAAW,cAAA0B,gBAAA,uBAAfA,gBAAA,CAAiBC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAClB,UAAU,CAACiB,WAAW,CAAC,CAAC,CAAC;IAAA,CACnE,CAAC;EACH,CAAC,EAAE,CAACvB,IAAI,EAAEM,UAAU,CAAC,CAAC;EAEtB,MAAMmB,SAAS,GAAGxE,IAAI,GAAG,CAAC,GAAGyE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG1E,IAAI,IAAIC,WAAW,GAAGiE,YAAY,CAACpF,MAAM,CAAC,GAAG,CAAC;EAC5F,MAAM6F,WAAW,GAAGzH,OAAO,CACzB,MAAMgH,YAAY,CAACjF,KAAK,CAACe,IAAI,GAAGC,WAAW,EAAED,IAAI,GAAGC,WAAW,GAAGA,WAAW,CAAC,EAC9E,CAACiE,YAAY,EAAElE,IAAI,EAAEC,WAAW,CAClC,CAAC;;EAED;EACA,MAAM2E,UAAU,GAAItD,EAAE,IAAK6B,QAAQ,CAAC0B,OAAO,CAACvD,EAAE,CAAC,KAAK,CAAC,CAAC;EACtD,MAAMwD,oBAAoB,GAAIC,KAAK,IAAK;IACtC,IAAIA,KAAK,CAACC,MAAM,CAACrF,OAAO,EAAE;MACxB,MAAMsF,WAAW,GAAGN,WAAW,CAACO,GAAG,CAAEzF,GAAG,IAAKA,GAAG,CAAC6B,EAAE,CAAC;MACpD8B,WAAW,CAAC,CAAC,GAAG,IAAI+B,GAAG,CAAC,CAAC,GAAGhC,QAAQ,EAAE,GAAG8B,WAAW,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC,MAAM;MACL,MAAMA,WAAW,GAAG9B,QAAQ,CAACgB,MAAM,CAAC7C,EAAE,IAAI,CAACqD,WAAW,CAACS,IAAI,CAAC3F,GAAG,IAAIA,GAAG,CAAC6B,EAAE,KAAKA,EAAE,CAAC,CAAC;MAClF8B,WAAW,CAAC6B,WAAW,CAAC;IAC1B;EACF,CAAC;EACD,MAAMrF,WAAW,GAAI0B,EAAE,IAAK;IAC1B,MAAM+D,aAAa,GAAGlC,QAAQ,CAAC0B,OAAO,CAACvD,EAAE,CAAC;IAC1C,IAAI2D,WAAW,GAAG,EAAE;IACpB,IAAII,aAAa,KAAK,CAAC,CAAC,EAAE;MACxBJ,WAAW,GAAGA,WAAW,CAACK,MAAM,CAACnC,QAAQ,EAAE7B,EAAE,CAAC;IAChD,CAAC,MAAM;MACL2D,WAAW,GAAG9B,QAAQ,CAACgB,MAAM,CAACoB,KAAK,IAAIA,KAAK,KAAKjE,EAAE,CAAC;IACtD;IACA8B,WAAW,CAAC6B,WAAW,CAAC;EAC1B,CAAC;EAED,MAAMO,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAIrC,QAAQ,CAACrE,MAAM,KAAK,CAAC,EAAE;IAC3B,MAAM2G,OAAO,GAAG,MAAM3H,IAAI,CAAC4H,IAAI,CAAC;MAC9BzD,KAAK,EAAE,eAAe;MACtB1D,IAAI,EAAE,yBAAyB4E,QAAQ,CAACrE,MAAM,mBAAmB;MACjE6G,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,WAAW;MAC9BC,gBAAgB,EAAE,OAAO;MACzBC,kBAAkB,EAAE,SAAS;MAC7BC,iBAAiB,EAAE;IACrB,CAAC,CAAC;IACF,IAAIP,OAAO,CAACQ,WAAW,EAAE;MACvB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC;MACzC,KAAK,MAAM9E,EAAE,IAAI6B,QAAQ,EAAE;QACzB,MAAMO,KAAK,CAAC,cAAcpC,EAAE,EAAE,EAAE;UAC9B+E,MAAM,EAAE,QAAQ;UAChBC,OAAO,EAAE;YAAE,eAAe,EAAE,UAAUJ,KAAK;UAAG;QAChD,CAAC,CAAC;MACJ;MACA9C,WAAW,CAAC,EAAE,CAAC;MACf;MACAM,KAAK,CAAC,YAAY,CAAC,CAChBC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACG,IAAI,IAAI;QACZ,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;UACvBd,OAAO,CAACc,IAAI,CAAC;QACf,CAAC,MAAM;UACLd,OAAO,CAAC,EAAE,CAAC;QACb;MACF,CAAC,CAAC,CACDiB,KAAK,CAAC,MAAMjB,OAAO,CAAC,EAAE,CAAC,CAAC;MAE3BlF,IAAI,CAAC4H,IAAI,CAAC;QACRzD,KAAK,EAAE,WAAW;QAClB1D,IAAI,EAAE,GAAG4E,QAAQ,CAACrE,MAAM,0BAA0B;QAClD6G,IAAI,EAAE,SAAS;QACfY,KAAK,EAAE,IAAI;QACXC,iBAAiB,EAAE;MACrB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAM3G,UAAU,GAAIyB,EAAE,IAAK;IACzBvB,QAAQ,CAAC,yBAAyBuB,EAAE,EAAE,CAAC;EACzC,CAAC;EAED,MAAMxB,YAAY,GAAG,MAAOwB,EAAE,IAAK;IACjC,MAAMmE,OAAO,GAAG,MAAM3H,IAAI,CAAC4H,IAAI,CAAC;MAC9BzD,KAAK,EAAE,eAAe;MACtB1D,IAAI,EAAE,mCAAmC;MACzCoH,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE,WAAW;MAC9BC,gBAAgB,EAAE,OAAO;MACzBC,kBAAkB,EAAE,SAAS;MAC7BC,iBAAiB,EAAE;IACrB,CAAC,CAAC;IACF,IAAIP,OAAO,CAACQ,WAAW,EAAE;MACvB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC;MACzC,MAAM1C,KAAK,CAAC,cAAcpC,EAAE,EAAE,EAAE;QAC9B+E,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE;UAAE,eAAe,EAAE,UAAUJ,KAAK;QAAG;MAChD,CAAC,CAAC;MACF;MACAxC,KAAK,CAAC,YAAY,CAAC,CAChBC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACvBF,IAAI,CAACG,IAAI,IAAI;QACZ,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;UACvBd,OAAO,CAACc,IAAI,CAAC;QACf,CAAC,MAAM;UACLd,OAAO,CAAC,EAAE,CAAC;QACb;MACF,CAAC,CAAC,CACDiB,KAAK,CAAC,MAAMjB,OAAO,CAAC,EAAE,CAAC,CAAC;MAE3BlF,IAAI,CAAC4H,IAAI,CAAC;QACRzD,KAAK,EAAE,WAAW;QAClB1D,IAAI,EAAE,yBAAyB;QAC/BoH,IAAI,EAAE,SAAS;QACfY,KAAK,EAAE,IAAI;QACXC,iBAAiB,EAAE;MACrB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMtG,UAAU,GAAIC,GAAG,IAAK;IAC1B,IAAI,CAACA,GAAG,EAAE,OAAO,EAAE;IACnB,MAAMC,CAAC,GAAG,IAAIC,IAAI,CAACF,GAAG,CAAC;IACvB,IAAIG,KAAK,CAACF,CAAC,CAAC,EAAE,OAAOD,GAAG;IACxB,MAAMI,KAAK,GAAG,CACZ,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACxC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CACzC;IACD,MAAMC,GAAG,GAAGJ,CAAC,CAACK,OAAO,CAAC,CAAC;IACvB,MAAMC,GAAG,GAAGH,KAAK,CAACH,CAAC,CAACO,QAAQ,CAAC,CAAC,CAAC;IAC/B,MAAMC,GAAG,GAAGR,CAAC,CAACS,WAAW,CAAC,CAAC;IAC3B,MAAMC,GAAG,GAAGV,CAAC,CAACW,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACpD,MAAMC,KAAK,GAAGd,CAAC,CAACe,UAAU,CAAC,CAAC,CAACH,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACxD,OAAO,GAAGT,GAAG,IAAIE,GAAG,IAAIE,GAAG,IAAIE,GAAG,IAAII,KAAK,EAAE;EAC/C,CAAC;EAED,oBACE7C,OAAA;IAAKU,SAAS,EAAC,4BAA4B;IAAAC,QAAA,gBAEzCX,OAAA;MAAKU,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9EX,OAAA,CAACjB,WAAW;QAAC,cAAW,YAAY;QAACoE,EAAE,EAAE;UAAEK,QAAQ,EAAE;YAAE4E,EAAE,EAAE,MAAM;YAAEC,EAAE,EAAE;UAAO;QAAE,CAAE;QAAA1H,QAAA,gBAChFX,OAAA,CAAChB,IAAI;UAACsJ,SAAS,EAAC,OAAO;UAACvF,KAAK,EAAC,SAAS;UAACwF,IAAI,EAAC,kBAAkB;UAAA5H,QAAA,EAAC;QAEhE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPhB,OAAA,CAACf,UAAU;UAAC8D,KAAK,EAAC,cAAc;UAAApC,QAAA,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eAGNhB,OAAA;MAAKU,SAAS,EAAC,sEAAsE;MAAAC,QAAA,gBACnFX,OAAA;QAAKU,SAAS,EAAC,qCAAqC;QAAAC,QAAA,eAClDX,OAAA;UAAKU,SAAS,EAAC,oEAAoE;UAAAC,QAAA,gBACjFX,OAAA;YAAAW,QAAA,gBACEX,OAAA;cAAIU,SAAS,EAAC,kDAAkD;cAAAC,QAAA,EAAC;YAEjE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLhB,OAAA;cAAGU,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAErC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNhB,OAAA;YAAKU,SAAS,EAAC,iCAAiC;YAAAC,QAAA,GAC7CmE,QAAQ,CAACrE,MAAM,GAAG,CAAC,iBAClBT,OAAA,CAACzB,MAAM;cACL8F,OAAO,EAAC,UAAU;cAClBtB,KAAK,EAAC,OAAO;cACb9B,OAAO,EAAEkG,oBAAqB;cAC9BhE,EAAE,EAAE;gBACFI,UAAU,EAAE,GAAG;gBACfW,YAAY,EAAE,CAAC;gBACfV,QAAQ,EAAE;kBAAE4E,EAAE,EAAE,MAAM;kBAAEC,EAAE,EAAE;gBAAO,CAAC;gBACpCG,EAAE,EAAE;kBAAEJ,EAAE,EAAE,CAAC;kBAAEC,EAAE,EAAE;gBAAI,CAAC;gBACtBI,EAAE,EAAE;kBAAEL,EAAE,EAAE,CAAC;kBAAEC,EAAE,EAAE;gBAAE;cACrB,CAAE;cAAA1H,QAAA,GACH,QACO,EAACmE,QAAQ,CAACrE,MAAM,EAAC,WACzB;YAAA;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,eACDhB,OAAA,CAACzB,MAAM;cACL8F,OAAO,EAAC,WAAW;cACnBtB,KAAK,EAAC,SAAS;cACf2F,SAAS,eAAE1I,OAAA,CAACxB,OAAO;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBmC,EAAE,EAAE;gBACFe,YAAY,EAAE,CAAC;gBACfyE,aAAa,EAAE,MAAM;gBACrBpF,UAAU,EAAE,GAAG;gBACfC,QAAQ,EAAE;kBAAE4E,EAAE,EAAE,MAAM;kBAAEC,EAAE,EAAE;gBAAO,CAAC;gBACpCG,EAAE,EAAE;kBAAEJ,EAAE,EAAE,CAAC;kBAAEC,EAAE,EAAE;gBAAI,CAAC;gBACtBI,EAAE,EAAE;kBAAEL,EAAE,EAAE,CAAC;kBAAEC,EAAE,EAAE;gBAAE,CAAC;gBACpBjE,SAAS,EAAE,uCAAuC;gBAClD,SAAS,EAAE;kBACTA,SAAS,EAAE;gBACb;cACF,CAAE;cACFnD,OAAO,EAAEA,CAAA,KAAMS,QAAQ,CAAC,2BAA2B,CAAE;cAAAf,QAAA,EACtD;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhB,OAAA;QAAKU,SAAS,EAAC,gDAAgD;QAAAC,QAAA,eAC7DX,OAAA;UAAKU,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9CX,OAAA;YAAKU,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BX,OAAA,CAACL,UAAU;cAACwD,EAAE,EAAE;gBACdyF,QAAQ,EAAE,UAAU;gBACpBC,IAAI,EAAE,EAAE;gBACRC,GAAG,EAAE,KAAK;gBACVC,SAAS,EAAE,kBAAkB;gBAC7BhG,KAAK,EAAE,SAAS;gBAChBS,QAAQ,EAAE;cACZ;YAAE;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACLhB,OAAA;cACEgJ,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,iDAAiD;cAC7DC,KAAK,EAAElE,UAAW;cAClBhC,QAAQ,EAAGmG,CAAC,IAAKlE,aAAa,CAACkE,CAAC,CAACxC,MAAM,CAACuC,KAAK,CAAE;cAC/CxI,SAAS,EAAC;YAAiJ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5J,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNhB,OAAA,CAACzB,MAAM;YACL8F,OAAO,EAAC,UAAU;YAClBqE,SAAS,eAAE1I,OAAA,CAACJ,cAAc;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC9BmC,EAAE,EAAE;cACFe,YAAY,EAAE,CAAC;cACfyE,aAAa,EAAE,MAAM;cACrBpF,UAAU,EAAE,GAAG;cACfC,QAAQ,EAAE;gBAAE4E,EAAE,EAAE,MAAM;gBAAEC,EAAE,EAAE;cAAO,CAAC;cACpCG,EAAE,EAAE;gBAAEJ,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE;cAAI,CAAC;cACtBI,EAAE,EAAE;gBAAEL,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE;cAAE;YACrB,CAAE;YAAA1H,QAAA,EACH;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhB,OAAA;QAAKU,SAAS,EAAC,qDAAqD;QAAAC,QAAA,eAClEX,OAAA;UAAKU,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDX,OAAA;YAAKU,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCX,OAAA,CAACf,UAAU;cAACkE,EAAE,EAAE;gBACdJ,KAAK,EAAE,SAAS;gBAChBS,QAAQ,EAAE;kBAAE4E,EAAE,EAAE,MAAM;kBAAEC,EAAE,EAAE;gBAAO,CAAC;gBACpC9E,UAAU,EAAE;cACd,CAAE;cAAA5C,QAAA,GACCkF,YAAY,CAACpF,MAAM,EAAC,mBACvB;YAAA;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EACZ8D,QAAQ,CAACrE,MAAM,GAAG,CAAC,iBAClBT,OAAA,CAACF,IAAI;cACHsD,KAAK,EAAE,GAAG0B,QAAQ,CAACrE,MAAM,WAAY;cACrC4C,IAAI,EAAC,OAAO;cACZN,KAAK,EAAC,SAAS;cACfI,EAAE,EAAE;gBAAEK,QAAQ,EAAE;cAAU;YAAE;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CACF;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACNhB,OAAA;YAAKU,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCX,OAAA;cAAMU,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAkB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjEhB,OAAA;cACEkJ,KAAK,EAAEtH,WAAY;cACnBoB,QAAQ,EAAGmG,CAAC,IAAK;gBAAEtE,cAAc,CAACuE,QAAQ,CAACD,CAAC,CAACxC,MAAM,CAACuC,KAAK,EAAE,EAAE,CAAC,CAAC;gBAAEtE,OAAO,CAAC,CAAC,CAAC;cAAE,CAAE;cAC/ElE,SAAS,EAAC,sGAAsG;cAAAC,QAAA,EAE/G,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAACkG,GAAG,CAAEwC,MAAM,iBAC1BrJ,OAAA;gBAAqBkJ,KAAK,EAAEG,MAAO;gBAAA1I,QAAA,EAAE0I;cAAM,GAA9BA,MAAM;gBAAAxI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiC,CACrD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhB,OAAA;MAAKU,SAAS,EAAC,sEAAsE;MAAAC,QAAA,GAClFP,QAAQ;MAAA;MACP;MACAJ,OAAA;QAAKU,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAElBX,OAAA;UAAKU,SAAS,EAAC,kEAAkE;UAAAC,QAAA,gBAC/EX,OAAA;YAAKU,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCX,OAAA,CAACN,QAAQ;cACPqD,KAAK,EAAC,SAAS;cACfuG,aAAa,EAAEhD,WAAW,CAACR,MAAM,CAAC1E,GAAG,IAAImF,UAAU,CAACnF,GAAG,CAAC6B,EAAE,CAAC,CAAC,CAACxC,MAAM,GAAG,CAAC,IAAI6F,WAAW,CAACR,MAAM,CAAC1E,GAAG,IAAImF,UAAU,CAACnF,GAAG,CAAC6B,EAAE,CAAC,CAAC,CAACxC,MAAM,GAAG6F,WAAW,CAAC7F,MAAO;cACrJa,OAAO,EAAEgF,WAAW,CAAC7F,MAAM,GAAG,CAAC,IAAI6F,WAAW,CAACR,MAAM,CAAC1E,GAAG,IAAImF,UAAU,CAACnF,GAAG,CAAC6B,EAAE,CAAC,CAAC,CAACxC,MAAM,KAAK6F,WAAW,CAAC7F,MAAO;cAC/GuC,QAAQ,EAAEyD,oBAAqB;cAC/BvD,UAAU,EAAE;gBAAE,YAAY,EAAE;cAAkB,CAAE;cAChDC,EAAE,EAAE;gBACF,eAAe,EAAE;kBACfJ,KAAK,EAAE;gBACT;cACF;YAAE;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFhB,OAAA;cAAMU,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAEpD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EACL8D,QAAQ,CAACrE,MAAM,GAAG,CAAC,iBAClBT,OAAA,CAACF,IAAI;YACHsD,KAAK,EAAE,GAAG0B,QAAQ,CAACrE,MAAM,WAAY;YACrC4C,IAAI,EAAC,OAAO;YACZN,KAAK,EAAC,SAAS;YACfI,EAAE,EAAE;cAAEK,QAAQ,EAAE;YAAU;UAAE;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAGLsF,WAAW,CAAC7F,MAAM,KAAK,CAAC,gBACvBT,OAAA;UAAKU,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BX,OAAA;YAAKU,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BX,OAAA,CAACL,UAAU;cAACwD,EAAE,EAAE;gBAAEK,QAAQ,EAAE,EAAE;gBAAET,KAAK,EAAE,SAAS;gBAAEwG,EAAE,EAAE;cAAE;YAAE;cAAA1I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7DhB,OAAA;cAAGU,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EACpCqE,UAAU,GAAG,2BAA2B,GAAG;YAAkB;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eACJhB,OAAA;cAAGU,SAAS,EAAC,SAAS;cAAAC,QAAA,EACnBqE,UAAU,GAAG,gCAAgC,GAAG;YAAgD;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,GAENsF,WAAW,CAACO,GAAG,CAAC,CAACzF,GAAG,EAAEC,GAAG,KAAK;UAC5B,MAAMC,OAAO,GAAGiF,UAAU,CAACnF,GAAG,CAAC6B,EAAE,CAAC;UAClC,oBACEjD,OAAA,CAACmB,cAAc;YAEbC,GAAG,EAAEA,GAAI;YACTC,GAAG,EAAEA,GAAI;YACTC,OAAO,EAAEA,OAAQ;YACjBC,WAAW,EAAEA,WAAY;YACzBC,UAAU,EAAEA,UAAW;YACvBC,YAAY,EAAEA,YAAa;YAC3BC,QAAQ,EAAEA,QAAS;YACnBC,IAAI,EAAEA,IAAK;YACXC,WAAW,EAAEA,WAAY;YACzBxB,QAAQ,EAAEA;UAAS,GAVdgB,GAAG,CAAC6B,EAAE,IAAI5B,GAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWnB,CAAC;QAEN,CAAC,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;MAAA;MAEN;MACAhB,OAAA;QAAKU,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BX,OAAA;UAAOU,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACvBX,OAAA;YAAOU,SAAS,EAAC,uDAAuD;YAAAC,QAAA,eACtEX,OAAA;cAAAW,QAAA,gBACEX,OAAA;gBAAIU,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,eAClCX,OAAA,CAACN,QAAQ;kBACPqD,KAAK,EAAC,SAAS;kBACfuG,aAAa,EAAEhD,WAAW,CAACR,MAAM,CAAC1E,GAAG,IAAImF,UAAU,CAACnF,GAAG,CAAC6B,EAAE,CAAC,CAAC,CAACxC,MAAM,GAAG,CAAC,IAAI6F,WAAW,CAACR,MAAM,CAAC1E,GAAG,IAAImF,UAAU,CAACnF,GAAG,CAAC6B,EAAE,CAAC,CAAC,CAACxC,MAAM,GAAG6F,WAAW,CAAC7F,MAAO;kBACrJa,OAAO,EAAEgF,WAAW,CAAC7F,MAAM,GAAG,CAAC,IAAI6F,WAAW,CAACR,MAAM,CAAC1E,GAAG,IAAImF,UAAU,CAACnF,GAAG,CAAC6B,EAAE,CAAC,CAAC,CAACxC,MAAM,KAAK6F,WAAW,CAAC7F,MAAO;kBAC/GuC,QAAQ,EAAEyD,oBAAqB;kBAC/BvD,UAAU,EAAE;oBAAE,YAAY,EAAE;kBAAkB,CAAE;kBAChDC,EAAE,EAAE;oBACFJ,KAAK,EAAE,MAAM;oBACb,eAAe,EAAE;sBAAEA,KAAK,EAAE;oBAAO,CAAC;oBAClC,6BAA6B,EAAE;sBAAEA,KAAK,EAAE;oBAAO;kBACjD;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACLhB,OAAA;gBAAIU,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClEhB,OAAA;gBAAIU,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpEhB,OAAA;gBAAIU,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5EhB,OAAA;gBAAIU,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChFhB,OAAA;gBAAIU,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzEhB,OAAA;gBAAIU,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EAAC;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRhB,OAAA;YAAOU,SAAS,EAAC,UAAU;YAAAC,QAAA,EACxB2F,WAAW,CAAC7F,MAAM,KAAK,CAAC,gBACvBT,OAAA;cAAAW,QAAA,eACEX,OAAA;gBAAIwJ,OAAO,EAAC,GAAG;gBAAC9I,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC1CX,OAAA;kBAAKU,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BX,OAAA,CAACL,UAAU;oBAACwD,EAAE,EAAE;sBAAEK,QAAQ,EAAE,EAAE;sBAAET,KAAK,EAAE,SAAS;sBAAEwG,EAAE,EAAE;oBAAE;kBAAE;oBAAA1I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC7DhB,OAAA;oBAAGU,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,EACpCqE,UAAU,GAAG,2BAA2B,GAAG;kBAAkB;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACJhB,OAAA;oBAAGU,SAAS,EAAC,SAAS;oBAAAC,QAAA,EACnBqE,UAAU,GAAG,gCAAgC,GAAG;kBAAgD;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GAELsF,WAAW,CAACO,GAAG,CAAC,CAACzF,GAAG,EAAEC,GAAG,KAAK;cAC5B,MAAMC,OAAO,GAAGiF,UAAU,CAACnF,GAAG,CAAC6B,EAAE,CAAC;cAClC,oBACEjD,OAAA;gBAAwBU,SAAS,EAAE,2DAA2DY,OAAO,GAAG,YAAY,GAAG,kBAAkB,EAAG;gBAAAX,QAAA,gBAC1IX,OAAA;kBAAIU,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,eAClCX,OAAA,CAACN,QAAQ;oBACPqD,KAAK,EAAC,SAAS;oBACfzB,OAAO,EAAEA,OAAQ;oBACjB0B,QAAQ,EAAEA,CAAA,KAAMzB,WAAW,CAACH,GAAG,CAAC6B,EAAE,CAAE;oBACpCC,UAAU,EAAE;sBAAE,YAAY,EAAE,cAAc7B,GAAG,GAAG,CAAC;oBAAG;kBAAE;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACLhB,OAAA;kBAAIU,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,eAClCX,OAAA,CAACF,IAAI;oBACHsD,KAAK,EAAEzB,IAAI,GAAGC,WAAW,GAAGP,GAAG,GAAG,CAAE;oBACpCgC,IAAI,EAAC,OAAO;oBACZF,EAAE,EAAE;sBACFG,OAAO,EAAE,SAAS;sBAClBP,KAAK,EAAE,SAAS;sBAChBQ,UAAU,EAAE,GAAG;sBACfC,QAAQ,EAAE;oBACZ;kBAAE;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACLhB,OAAA;kBAAIU,SAAS,EAAC,UAAU;kBAAAC,QAAA,eACtBX,OAAA,CAACZ,MAAM;oBACLuE,GAAG,EAAEvC,GAAG,CAACwC,KAAM;oBACfC,GAAG,EAAEzC,GAAG,CAAC0C,KAAK,GAAI1C,GAAG,CAAC0C,KAAK,CAACC,UAAU,CAAC,WAAW,CAAC,GAAG3C,GAAG,CAAC0C,KAAK,GAAG,YAAY1C,GAAG,CAAC0C,KAAK,EAAE,GAAI,EAAG;oBAChGX,EAAE,EAAE;sBACFa,KAAK,EAAE,EAAE;sBACTC,MAAM,EAAE,EAAE;sBACVC,YAAY,EAAE,CAAC;sBACfC,MAAM,EAAE,mBAAmB;sBAC3BC,SAAS,EAAE;oBACb,CAAE;oBACFC,OAAO,EAAC;kBAAQ;oBAAAxD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACLhB,OAAA;kBAAIU,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,eAC/BX,OAAA;oBAAGU,SAAS,EAAC,iDAAiD;oBAAAC,QAAA,EAC3DS,GAAG,CAACwC;kBAAK;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACLhB,OAAA;kBAAIU,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,eAC/BX,OAAA,CAACC,aAAa;oBAACC,IAAI,EAAEkB,GAAG,CAACkD,WAAY;oBAACnE,SAAS,EAAE,GAAI;oBAACC,QAAQ,EAAEA;kBAAS;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E,CAAC,eACLhB,OAAA;kBAAIU,SAAS,EAAC,UAAU;kBAAAC,QAAA,eACtBX,OAAA;oBAAGU,SAAS,EAAC,yFAAyF;oBAAAC,QAAA,EACnGkB,UAAU,CAACT,GAAG,CAACqC,IAAI,IAAIrC,GAAG,CAACsC,UAAU;kBAAC;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACLhB,OAAA;kBAAIU,SAAS,EAAC,UAAU;kBAAAC,QAAA,eACtBX,OAAA;oBAAKU,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,gBACxCX,OAAA,CAACX,UAAU;sBACT0D,KAAK,EAAC,MAAM;sBACZM,IAAI,EAAC,OAAO;sBACZpC,OAAO,EAAEA,CAAA,KAAMS,QAAQ,CAAC,4BAA4BN,GAAG,CAAC6B,EAAE,EAAE,CAAE;sBAC9DE,EAAE,EAAE;wBACFG,OAAO,EAAE,SAAS;wBAClB,SAAS,EAAE;0BAAEA,OAAO,EAAE;wBAAU,CAAC;wBACjCU,KAAK,EAAE,EAAE;wBACTC,MAAM,EAAE,EAAE;wBACVC,YAAY,EAAE;sBAChB,CAAE;sBAAAvD,QAAA,eAEFX,OAAA,CAACR,cAAc;wBAAC2D,EAAE,EAAE;0BAAEK,QAAQ,EAAE;wBAAG;sBAAE;wBAAA3C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CAAC,eACbhB,OAAA,CAACX,UAAU;sBACT0D,KAAK,EAAC,SAAS;sBACfM,IAAI,EAAC,OAAO;sBACZpC,OAAO,EAAEA,CAAA,KAAMO,UAAU,CAACJ,GAAG,CAAC6B,EAAE,CAAE;sBAClCE,EAAE,EAAE;wBACFG,OAAO,EAAE,SAAS;wBAClB,SAAS,EAAE;0BAAEA,OAAO,EAAE;wBAAU,CAAC;wBACjCU,KAAK,EAAE,EAAE;wBACTC,MAAM,EAAE,EAAE;wBACVC,YAAY,EAAE;sBAChB,CAAE;sBAAAvD,QAAA,eAEFX,OAAA,CAACV,QAAQ;wBAAC6D,EAAE,EAAE;0BAAEK,QAAQ,EAAE;wBAAG;sBAAE;wBAAA3C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACbhB,OAAA,CAACX,UAAU;sBACT0D,KAAK,EAAC,OAAO;sBACbM,IAAI,EAAC,OAAO;sBACZpC,OAAO,EAAEA,CAAA,KAAMQ,YAAY,CAACL,GAAG,CAAC6B,EAAE,CAAE;sBACpCE,EAAE,EAAE;wBACFG,OAAO,EAAE,SAAS;wBAClB,SAAS,EAAE;0BAAEA,OAAO,EAAE;wBAAU,CAAC;wBACjCU,KAAK,EAAE,EAAE;wBACTC,MAAM,EAAE,EAAE;wBACVC,YAAY,EAAE;sBAChB,CAAE;sBAAAvD,QAAA,eAEFX,OAAA,CAACT,UAAU;wBAAC4D,EAAE,EAAE;0BAAEK,QAAQ,EAAE;wBAAG;sBAAE;wBAAA3C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA,GA7FEI,GAAG,CAAC6B,EAAE,IAAI5B,GAAG;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA8FlB,CAAC;YAET,CAAC;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN,eAGDhB,OAAA;QAAKU,SAAS,EAAC,uDAAuD;QAAAC,QAAA,eACpEX,OAAA;UAAKU,SAAS,EAAC,oEAAoE;UAAAC,QAAA,gBACjFX,OAAA;YAAKU,SAAS,EAAC,yBAAyB;YAAAC,QAAA,eACtCX,OAAA;cAAMU,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,cAC1B,EAACgB,IAAI,GAAGC,WAAW,GAAG,CAAC,EAAC,GAAC,EAACwE,IAAI,CAACqD,GAAG,CAAC,CAAC9H,IAAI,GAAG,CAAC,IAAIC,WAAW,EAAEiE,YAAY,CAACpF,MAAM,CAAC,EAAC,QAAM,EAACoF,YAAY,CAACpF,MAAM,EAAC,SAC3H;YAAA;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNhB,OAAA;YAAKU,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBX,OAAA;cACEiB,OAAO,EAAEA,CAAA,KAAM2D,OAAO,CAACwB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE1E,IAAI,GAAG,CAAC,CAAC,CAAE;cAC9C+H,QAAQ,EAAE/H,IAAI,KAAK,CAAE;cACrBjB,SAAS,EAAC,qJAAqJ;cAAAC,QAAA,EAChK;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThB,OAAA;cACEiB,OAAO,EAAEA,CAAA,KAAM2D,OAAO,CAACjD,IAAI,GAAG,CAAC,CAAE;cACjC+H,QAAQ,EAAE,CAAC/H,IAAI,GAAG,CAAC,IAAIC,WAAW,IAAIiE,YAAY,CAACpF,MAAO;cAC1DC,SAAS,EAAC,qJAAqJ;cAAAC,QAAA,EAChK;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACyD,GAAA,CAniBuBD,QAAQ;EAAA,QAMb/F,WAAW,EACdU,QAAQ,EACLD,aAAa;AAAA;AAAAyK,GAAA,GARRnF,QAAQ;AAAA,IAAAtD,EAAA,EAAAqD,GAAA,EAAAoF,GAAA;AAAAC,YAAA,CAAA1I,EAAA;AAAA0I,YAAA,CAAArF,GAAA;AAAAqF,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}