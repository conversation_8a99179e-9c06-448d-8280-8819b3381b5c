{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Views from './pages/admin/views';\nimport LandingPage from './pages/user/LandingPage';\nimport Saved from './pages/user/components/Saved';\nimport './App.css';\nimport Login from './pages/admin/auth/Login';\nimport Register from './pages/admin/auth/Register';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(LandingPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/admin\",\n        element: /*#__PURE__*/_jsxDEV(Views, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/admin/login\",\n        element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 45\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/admin/register\",\n        element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 48\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/saved\",\n        element: /*#__PURE__*/_jsxDEV(Saved, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Views", "LandingPage", "Saved", "<PERSON><PERSON>", "Register", "jsxDEV", "_jsxDEV", "App", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport Views from './pages/admin/views';\nimport LandingPage from './pages/user/LandingPage';\nimport Saved from './pages/user/components/Saved';\nimport './App.css';\nimport Login from './pages/admin/auth/Login';\nimport Register from './pages/admin/auth/Register';\n\nfunction App() {\n  return (\n    <Router>\n      <Routes>\n        <Route path=\"/\" element={<LandingPage />} />\n        <Route path=\"/admin\" element={<Views />} />\n        <Route path=\"/admin/login\" element={<Login />} />\n        <Route path=\"/admin/register\" element={<Register />} />\n        <Route path=\"/saved\" element={<Saved />} />\n      </Routes>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,KAAK,MAAM,+BAA+B;AACjD,OAAO,WAAW;AAClB,OAAOC,KAAK,MAAM,0BAA0B;AAC5C,OAAOC,QAAQ,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACT,MAAM;IAAAW,QAAA,eACLF,OAAA,CAACR,MAAM;MAAAU,QAAA,gBACLF,OAAA,CAACP,KAAK;QAACU,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEJ,OAAA,CAACL,WAAW;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5CR,OAAA,CAACP,KAAK;QAACU,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEJ,OAAA,CAACN,KAAK;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3CR,OAAA,CAACP,KAAK;QAACU,IAAI,EAAC,cAAc;QAACC,OAAO,eAAEJ,OAAA,CAACH,KAAK;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjDR,OAAA,CAACP,KAAK;QAACU,IAAI,EAAC,iBAAiB;QAACC,OAAO,eAAEJ,OAAA,CAACF,QAAQ;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvDR,OAAA,CAACP,KAAK;QAACU,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEJ,OAAA,CAACJ,KAAK;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb;AAACC,EAAA,GAZQR,GAAG;AAcZ,eAAeA,GAAG;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}