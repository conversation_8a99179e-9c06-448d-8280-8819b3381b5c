{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M18 6.41 16.59 5 12 9.58 7.41 5 6 6.41l6 6z\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"m18 13-1.41-1.41L12 16.17l-4.59-4.58L6 13l6 6z\"\n}, \"1\")], 'KeyboardDoubleArrowDown');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/laragon/www/react-news/frontend/node_modules/@mui/icons-material/esm/KeyboardDoubleArrowDown.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M18 6.41 16.59 5 12 9.58 7.41 5 6 6.41l6 6z\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"m18 13-1.41-1.41L12 16.17l-4.59-4.58L6 13l6 6z\"\n}, \"1\")], 'KeyboardDoubleArrowDown');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,0BAA0B;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,yBAAyB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}