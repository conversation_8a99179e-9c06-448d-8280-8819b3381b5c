import React, { useState } from 'react';
import { Box, TextField, Button, Typography, Paper, Modal, IconButton } from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import MySwal from '../swal';

export default function Register() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [jwtToken, setJwtToken] = useState('');
  const [loading, setLoading] = useState(false);
  const [showTokenModal, setShowTokenModal] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');
    try {
      // Replace with your backend API endpoint
      const res = await fetch('/api/admin/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username, password })
      });
      const data = await res.json();
      if (res.ok && data.token) {
        setSuccess('Registrasi berhasil!');
        setJwtToken(data.token);
        setUsername('');
        setPassword('');
        MySwal.fire({
          title: 'Registrasi Berhasil!',
          icon: 'success',
          showConfirmButton: false,
          timer: 1200
        }).then(() => {
          setShowTokenModal(true);
        });
      } else {
        setError(data.message || 'Registrasi gagal');
      }
    } catch (err) {
      setError('Terjadi kesalahan server');
    }
    setLoading(false);
  };

  return (
    <>
      <Box sx={{ minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'grey.100' }}>
        <Paper elevation={3} sx={{ p: 4, minWidth: 320, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
            <img src="/logo192.png" alt="Logo" style={{ width: 64, height: 64 }} />
          </Box>
          <Typography variant="h5" sx={{ mb: 2, fontWeight: 700, textAlign: 'center' }}>Admin Register</Typography>
          <form onSubmit={handleSubmit} style={{ width: '100%' }}>
            <TextField label="Username" fullWidth sx={{ mb: 2 }} value={username} onChange={e => setUsername(e.target.value)} required />
            <TextField label="Password" type="password" fullWidth sx={{ mb: 2 }} value={password} onChange={e => setPassword(e.target.value)} required />
            {error && <Typography color="error" sx={{ mb: 2 }}>{error}</Typography>}
            {success && <Typography color="primary" sx={{ mb: 2 }}>{success}</Typography>}
            <Button type="submit" variant="contained" color="primary" fullWidth disabled={loading}>{loading ? 'Loading...' : 'Register'}</Button>
          </form>
          <Box sx={{ mt: 3, textAlign: 'center', width: '100%' }}>
            <Typography variant="body2">
              Sudah punya akun?{' '}
              <a href="/admin/login" style={{ color: '#2563eb', textDecoration: 'none', fontWeight: 600 }}>Login di sini</a>
            </Typography>
          </Box>
        </Paper>
      </Box>
      <Modal open={showTokenModal} onClose={() => { setShowTokenModal(false); window.location.href = '/admin/login'; }}>
        <Box sx={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', bgcolor: 'background.paper', boxShadow: 24, borderRadius: 2, p: 4, minWidth: 340, maxWidth: '90vw', outline: 'none' }}>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 700, textAlign: 'center' }}>Token JWT Anda</Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <TextField
              value={jwtToken}
              InputProps={{ readOnly: true }}
              multiline
              minRows={3}
              fullWidth
              sx={{ fontFamily: 'monospace', bgcolor: '#f5f5f5', borderRadius: 1 }}
            />
            <IconButton aria-label="copy" onClick={() => {navigator.clipboard.writeText(jwtToken)}} sx={{ ml: 1 }}>
              <ContentCopyIcon />
            </IconButton>
          </Box>
          <Button variant="outlined" color="primary" fullWidth onClick={() => { setShowTokenModal(false); window.location.href = '/admin/login'; }}>Tutup</Button>
        </Box>
      </Modal>
    </>
  );
}
