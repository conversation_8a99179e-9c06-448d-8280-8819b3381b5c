import Button from '@mui/material/Button';
import AddIcon from '@mui/icons-material/Add';
import { useNavigate } from 'react-router-dom';
import React, { useEffect, useState, useMemo } from 'react';
import Box from '@mui/material/Box';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import Link from '@mui/material/Link';
import Typography from '@mui/material/Typography';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTheme } from '@mui/material/styles';
import Avatar from '@mui/material/Avatar';
import IconButton from '@mui/material/IconButton';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import VisibilityIcon from '@mui/icons-material/Visibility';
import Swal from 'sweetalert2';
import Checkbox from '@mui/material/Checkbox';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Chip from '@mui/material/Chip';

function DeskripsiCell({ text, maxLength, isMobile }) {
  const [expanded, setExpanded] = useState(false);
  if (!text) return '';
  const isLong = text.length > maxLength;
  return (
    <div className="flex flex-col gap-1">
      <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-700 leading-tight ${expanded ? 'whitespace-normal' : 'truncate'}`}>
        {expanded || !isLong ? text : text.slice(0, maxLength) + '...'}
      </p>
      {isLong && (
        <button 
          className={`text-blue-600 hover:text-blue-800 text-xs font-medium self-start transition-colors`}
          onClick={() => setExpanded(!expanded)}
        >
          {expanded ? 'Tutup' : 'Baca'}
        </button>
      )}
    </div>
  );
}

// Mobile Card Component
function MobileNewsCard({ row, idx, checked, handleClick, handleEdit, handleDelete, navigate, page, rowsPerPage, isMobile }) {
  const formatDate = (raw) => {
    if (!raw) return '';
    const d = new Date(raw);
    if (isNaN(d)) return raw;
    const bulan = [
      'Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun',
      'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'
    ];
    const tgl = d.getDate();
    const bln = bulan[d.getMonth()];
    const thn = d.getFullYear();
    const jam = d.getHours().toString().padStart(2, '0');
    const menit = d.getMinutes().toString().padStart(2, '0');
    return `${tgl}/${bln} ${jam}:${menit}`;
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-4 mb-3 transition-all duration-200 ${checked ? 'ring-2 ring-blue-500 bg-blue-50 shadow-lg' : 'hover:bg-gray-50 hover:shadow-md'}`}>
      {/* Header with checkbox and number */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-3">
          <Checkbox
            color="primary"
            checked={checked}
            onChange={() => handleClick(row.id)}
            inputProps={{ 'aria-label': `select row ${idx + 1}` }}
            sx={{
              '&.Mui-checked': {
                color: '#2563eb',
              },
            }}
          />
          <Chip 
            label={`#${page * rowsPerPage + idx + 1}`}
            size="small"
            sx={{ 
              bgcolor: '#f1f5f9', 
              color: '#475569',
              fontWeight: 600,
              fontSize: '0.75rem'
            }}
          />
        </div>
        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
          {formatDate(row.date || row.created_at)}
        </span>
      </div>

      {/* Content */}
      <div className="flex gap-3">
        {/* Image */}
        <div className="flex-shrink-0">
          <Avatar
            alt={row.title}
            src={row.image ? (row.image.startsWith('/uploads/') ? row.image : `/uploads/${row.image}`) : ''}
            sx={{ 
              width: 60, 
              height: 60, 
              borderRadius: 2,
              border: '2px solid #e2e8f0',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}
            variant="square"
          />
        </div>

        {/* Text Content */}
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-gray-900 text-sm mb-1 truncate">
            {row.title}
          </h3>
          <p className="text-xs text-gray-600 mb-3 line-clamp-2">
            {row.description}
          </p>
          
          {/* Action Buttons */}
          <div className="flex gap-2">
            <button
              onClick={() => navigate(`/admin/pages/previewnews/${row.id}`)}
              className="flex items-center gap-1 px-3 py-1.5 text-xs bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-all duration-200 font-medium"
            >
              <VisibilityIcon sx={{ fontSize: 14 }} />
              Lihat
            </button>
            <button
              onClick={() => handleEdit(row.id)}
              className="flex items-center gap-1 px-3 py-1.5 text-xs bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-all duration-200 font-medium"
            >
              <EditIcon sx={{ fontSize: 14 }} />
              Edit
            </button>
            <button
              onClick={() => handleDelete(row.id)}
              className="flex items-center gap-1 px-3 py-1.5 text-xs bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-all duration-200 font-medium"
            >
              <DeleteIcon sx={{ fontSize: 14 }} />
              Hapus
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function NewsPage() {
  const [rows, setRows] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [selected, setSelected] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  useEffect(() => {
    fetch('/api/posts')
      .then(res => res.json())
      .then(data => {
        if (Array.isArray(data)) {
          setRows(data);
        } else {
          setRows([]);
        }
      })
      .catch(() => setRows([]));
  }, []);

  // Filter rows based on search term
  const filteredRows = useMemo(() => {
    if (!searchTerm) return rows;
    return rows.filter(row => 
      row.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      row.description?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [rows, searchTerm]);

  const emptyRows = page > 0 ? Math.max(0, (1 + page) * rowsPerPage - filteredRows.length) : 0;
  const visibleRows = useMemo(
    () => filteredRows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage),
    [filteredRows, page, rowsPerPage]
  );

  // Checkbox logic
  const isSelected = (id) => selected.indexOf(id) !== -1;
  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelected = visibleRows.map((row) => row.id);
      setSelected([...new Set([...selected, ...newSelected])]);
    } else {
      const newSelected = selected.filter(id => !visibleRows.some(row => row.id === id));
      setSelected(newSelected);
    }
  };
  const handleClick = (id) => {
    const selectedIndex = selected.indexOf(id);
    let newSelected = [];
    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id);
    } else {
      newSelected = selected.filter(selId => selId !== id);
    }
    setSelected(newSelected);
  };

  const handleDeleteSelected = async () => {
    if (selected.length === 0) return;
    const confirm = await Swal.fire({
      title: 'Hapus Berita?',
      text: `Yakin ingin menghapus ${selected.length} berita terpilih?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Ya, hapus',
      cancelButtonText: 'Batal',
      confirmButtonColor: '#dc2626',
      cancelButtonColor: '#6b7280',
    });
    if (confirm.isConfirmed) {
      const token = localStorage.getItem('jwt');
      for (const id of selected) {
        await fetch(`/api/posts/${id}`, {
          method: 'DELETE',
          headers: { 'Authorization': `Bearer ${token}` }
        });
      }
      setSelected([]);
      // Refresh data
      fetch('/api/posts')
        .then(res => res.json())
        .then(data => {
          if (Array.isArray(data)) {
            setRows(data);
          } else {
            setRows([]);
          }
        })
        .catch(() => setRows([]));
      
      Swal.fire({
        title: 'Berhasil!',
        text: `${selected.length} berita berhasil dihapus`,
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
      });
    }
  };

  const handleEdit = (id) => {
    navigate(`/admin/pages/editnews/${id}`);
  };

  const handleDelete = async (id) => {
    const confirm = await Swal.fire({
      title: 'Hapus Berita?',
      text: 'Yakin ingin menghapus berita ini?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Ya, hapus',
      cancelButtonText: 'Batal',
      confirmButtonColor: '#dc2626',
      cancelButtonColor: '#6b7280',
    });
    if (confirm.isConfirmed) {
      const token = localStorage.getItem('jwt');
      await fetch(`/api/posts/${id}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });
      // Refresh data
      fetch('/api/posts')
        .then(res => res.json())
        .then(data => {
          if (Array.isArray(data)) {
            setRows(data);
          } else {
            setRows([]);
          }
        })
        .catch(() => setRows([]));
      
      Swal.fire({
        title: 'Berhasil!',
        text: 'Berita berhasil dihapus',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
      });
    }
  };

  const formatDate = (raw) => {
    if (!raw) return '';
    const d = new Date(raw);
    if (isNaN(d)) return raw;
    const bulan = [
      'Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun',
      'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'
    ];
    const tgl = d.getDate();
    const bln = bulan[d.getMonth()];
    const thn = d.getFullYear();
    const jam = d.getHours().toString().padStart(2, '0');
    const menit = d.getMinutes().toString().padStart(2, '0');
    return `${tgl}/${bln}/${thn} ${jam}:${menit}`;
  };

  return (
    <div className="w-full flex flex-col gap-4">
      {/* Breadcrumb */}
      <div className="p-4 md:p-6 bg-white rounded-lg border border-gray-200 shadow-sm">
        <Breadcrumbs aria-label="breadcrumb" sx={{ fontSize: { xs: '12px', md: '14px' } }}>
          <Link underline="hover" color="inherit" href="/admin/dashboard">
            Dashboard
          </Link>
          <Typography color="text.primary">Berita</Typography>
        </Breadcrumbs>
      </div>

      {/* Header Actions */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="p-4 md:p-6 border-b border-gray-200">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <h2 className="text-xl md:text-2xl font-bold text-gray-800 mb-1">
                Daftar Berita
              </h2>
              <p className="text-gray-600 text-sm">
                Kelola dan atur semua berita dalam sistem
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-3">
              {selected.length > 0 && (
                <Button
                  variant="outlined"
                  color="error"
                  onClick={handleDeleteSelected}
                  sx={{ 
                    fontWeight: 600, 
                    borderRadius: 2,
                    fontSize: { xs: '12px', md: '14px' },
                    py: { xs: 1, md: 1.5 },
                    px: { xs: 2, md: 3 }
                  }}
                >
                  Hapus {selected.length} Terpilih
                </Button>
              )}
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                sx={{ 
                  borderRadius: 2, 
                  textTransform: 'none', 
                  fontWeight: 600,
                  fontSize: { xs: '12px', md: '14px' },
                  py: { xs: 1, md: 1.5 },
                  px: { xs: 2, md: 3 },
                  boxShadow: '0 4px 6px -1px rgba(37, 99, 235, 0.2)',
                  '&:hover': {
                    boxShadow: '0 6px 8px -1px rgba(37, 99, 235, 0.3)',
                  }
                }}
                onClick={() => navigate('/admin/pages/tambahberita')}
              >
                Tambah Berita
              </Button>
            </div>
          </div>
        </div>

        {/* Search and Filter Bar */}
        <div className="p-4 md:p-6 bg-gray-50 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="flex-1 relative">
              <SearchIcon sx={{ 
                position: 'absolute', 
                left: 12, 
                top: '50%', 
                transform: 'translateY(-50%)', 
                color: '#9ca3af',
                fontSize: 20
              }} />
              <input
                type="text"
                placeholder="Cari berita berdasarkan judul atau deskripsi..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
              />
            </div>
            <Button
              variant="outlined"
              startIcon={<FilterListIcon />}
              sx={{ 
                borderRadius: 2,
                textTransform: 'none',
                fontWeight: 500,
                fontSize: { xs: '12px', md: '14px' },
                py: { xs: 1, md: 1.5 },
                px: { xs: 2, md: 3 }
              }}
            >
              Filter
            </Button>
          </div>
        </div>

        {/* Results Summary */}
        <div className="px-4 md:px-6 py-3 bg-white border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Typography sx={{ 
                color: '#6b7280', 
                fontSize: { xs: '12px', md: '14px' },
                fontWeight: 500
              }}>
                {filteredRows.length} berita ditemukan
              </Typography>
              {selected.length > 0 && (
                <Chip 
                  label={`${selected.length} terpilih`}
                  size="small"
                  color="primary"
                  sx={{ fontSize: '0.75rem' }}
                />
              )}
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Baris per halaman:</span>
              <select 
                value={rowsPerPage} 
                onChange={(e) => { setRowsPerPage(parseInt(e.target.value, 10)); setPage(0); }}
                className="border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {[5, 10, 25, 50].map((option) => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Content - Mobile Cards or Desktop Table */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        {isMobile ? (
          /* Mobile Card View */
          <div className="p-4">
            {/* Mobile Select All Header */}
            <div className="flex items-center justify-between mb-4 p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <Checkbox
                  color="primary"
                  indeterminate={visibleRows.filter(row => isSelected(row.id)).length > 0 && visibleRows.filter(row => isSelected(row.id)).length < visibleRows.length}
                  checked={visibleRows.length > 0 && visibleRows.filter(row => isSelected(row.id)).length === visibleRows.length}
                  onChange={handleSelectAllClick}
                  inputProps={{ 'aria-label': 'select all news' }}
                  sx={{
                    '&.Mui-checked': {
                      color: '#2563eb',
                    },
                  }}
                />
                <span className="text-sm font-medium text-gray-700">
                  Pilih Semua
                </span>
              </div>
              {selected.length > 0 && (
                <Chip
                  label={`${selected.length} terpilih`}
                  size="small"
                  color="primary"
                  sx={{ fontSize: '0.75rem' }}
                />
              )}
            </div>

            {/* Mobile Cards */}
            {visibleRows.length === 0 ? (
              <div className="p-12 text-center">
                <div className="text-gray-500">
                  <SearchIcon sx={{ fontSize: 48, color: '#d1d5db', mb: 2 }} />
                  <p className="text-lg font-medium mb-2">
                    {searchTerm ? 'Tidak ada hasil pencarian' : 'Belum ada berita'}
                  </p>
                  <p className="text-sm">
                    {searchTerm ? 'Coba ubah kata kunci pencarian' : 'Berita akan muncul di sini setelah ditambahkan'}
                  </p>
                </div>
              </div>
            ) : (
              visibleRows.map((row, idx) => {
                const checked = isSelected(row.id);
                return (
                  <MobileNewsCard
                    key={row.id || idx}
                    row={row}
                    idx={idx}
                    checked={checked}
                    handleClick={handleClick}
                    handleEdit={handleEdit}
                    handleDelete={handleDelete}
                    navigate={navigate}
                    page={page}
                    rowsPerPage={rowsPerPage}
                    isMobile={isMobile}
                  />
                );
              })
            )}
          </div>
        ) : (
          /* Desktop Table View */
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gradient-to-r from-gray-800 to-gray-900 text-white">
                <tr>
                  <th className="w-16 p-4 text-center">
                    <Checkbox
                      color="primary"
                      indeterminate={visibleRows.filter(row => isSelected(row.id)).length > 0 && visibleRows.filter(row => isSelected(row.id)).length < visibleRows.length}
                      checked={visibleRows.length > 0 && visibleRows.filter(row => isSelected(row.id)).length === visibleRows.length}
                      onChange={handleSelectAllClick}
                      inputProps={{ 'aria-label': 'select all news' }}
                      sx={{
                        color: '#fff',
                        '&.Mui-checked': { color: '#fff' },
                        '&.MuiCheckbox-indeterminate': { color: '#fff' }
                      }}
                    />
                  </th>
                  <th className="w-20 p-4 text-center font-semibold text-sm">No</th>
                  <th className="w-24 p-4 text-left font-semibold text-sm">Gambar</th>
                  <th className="min-w-[200px] p-4 text-left font-semibold text-sm">Judul</th>
                  <th className="min-w-[300px] p-4 text-left font-semibold text-sm">Deskripsi</th>
                  <th className="w-40 p-4 text-left font-semibold text-sm">Tgl & Waktu</th>
                  <th className="w-32 p-4 text-center font-semibold text-sm">Aksi</th>
                </tr>
              </thead>
              <tbody className="bg-white">
                {visibleRows.length === 0 ? (
                  <tr>
                    <td colSpan="7" className="p-12 text-center">
                      <div className="text-gray-500">
                        <SearchIcon sx={{ fontSize: 48, color: '#d1d5db', mb: 2 }} />
                        <p className="text-lg font-medium mb-2">
                          {searchTerm ? 'Tidak ada hasil pencarian' : 'Belum ada berita'}
                        </p>
                        <p className="text-sm">
                          {searchTerm ? 'Coba ubah kata kunci pencarian' : 'Berita akan muncul di sini setelah ditambahkan'}
                        </p>
                      </div>
                    </td>
                  </tr>
                ) : (
                  visibleRows.map((row, idx) => {
                    const checked = isSelected(row.id);
                    return (
                      <tr key={row.id || idx} className={`border-b border-gray-100 transition-colors duration-200 ${checked ? 'bg-blue-50' : 'hover:bg-gray-50'}`}>
                        <td className="w-16 p-4 text-center">
                          <Checkbox
                            color="primary"
                            checked={checked}
                            onChange={() => handleClick(row.id)}
                            inputProps={{ 'aria-label': `select row ${idx + 1}` }}
                          />
                        </td>
                        <td className="w-20 p-4 text-center">
                          <Chip
                            label={page * rowsPerPage + idx + 1}
                            size="small"
                            sx={{
                              bgcolor: '#f1f5f9',
                              color: '#475569',
                              fontWeight: 600,
                              fontSize: '0.75rem'
                            }}
                          />
                        </td>
                        <td className="w-24 p-4">
                          <Avatar
                            alt={row.title}
                            src={row.image ? (row.image.startsWith('/uploads/') ? row.image : `/uploads/${row.image}`) : ''}
                            sx={{
                              width: 48,
                              height: 48,
                              borderRadius: 2,
                              border: '2px solid #e2e8f0',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                            }}
                            variant="square"
                          />
                        </td>
                        <td className="min-w-[200px] p-4">
                          <p className="text-sm font-semibold text-gray-900 break-words">
                            {row.title}
                          </p>
                        </td>
                        <td className="min-w-[300px] p-4">
                          <DeskripsiCell text={row.description} maxLength={100} isMobile={isMobile} />
                        </td>
                        <td className="w-40 p-4">
                          <p className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded-full inline-block whitespace-nowrap">
                            {formatDate(row.date || row.created_at)}
                          </p>
                        </td>
                        <td className="w-32 p-4">
                          <div className="flex justify-center gap-1">
                            <IconButton
                              color="info"
                              size="small"
                              onClick={() => navigate(`/admin/pages/previewnews/${row.id}`)}
                              sx={{
                                bgcolor: '#eff6ff',
                                '&:hover': { bgcolor: '#dbeafe' },
                                width: 32,
                                height: 32,
                                borderRadius: 1.5
                              }}
                            >
                              <VisibilityIcon sx={{ fontSize: 16 }} />
                            </IconButton>
                            <IconButton
                              color="primary"
                              size="small"
                              onClick={() => handleEdit(row.id)}
                              sx={{
                                bgcolor: '#f0fdf4',
                                '&:hover': { bgcolor: '#dcfce7' },
                                width: 32,
                                height: 32,
                                borderRadius: 1.5
                              }}
                            >
                              <EditIcon sx={{ fontSize: 16 }} />
                            </IconButton>
                            <IconButton
                              color="error"
                              size="small"
                              onClick={() => handleDelete(row.id)}
                              sx={{
                                bgcolor: '#fef2f2',
                                '&:hover': { bgcolor: '#fecaca' },
                                width: 32,
                                height: 32,
                                borderRadius: 1.5
                              }}
                            >
                              <DeleteIcon sx={{ fontSize: 16 }} />
                            </IconButton>
                          </div>
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination */}
        <div className="px-4 md:px-6 py-4 bg-gray-50 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-700">
                Menampilkan {page * rowsPerPage + 1}-{Math.min((page + 1) * rowsPerPage, filteredRows.length)} dari {filteredRows.length} berita
              </span>
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => setPage(Math.max(0, page - 1))}
                disabled={page === 0}
                className="px-4 py-2 text-sm border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 transition-colors font-medium"
              >
                Sebelumnya
              </button>
              <button
                onClick={() => setPage(page + 1)}
                disabled={(page + 1) * rowsPerPage >= filteredRows.length}
                className="px-4 py-2 text-sm border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 transition-colors font-medium"
              >
                Selanjutnya
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
