import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  IconButton,
  Avatar,
  Chip,
  Button,
  TextField,
  Switch,
  FormControlLabel,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  AppBar,
  Toolbar,
  useMediaQuery,
  useTheme,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  Paper,
  Stack,
  Menu,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  TablePagination,
  InputAdornment
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Article as NewsIcon,
  Settings as SettingsIcon,
  Menu as MenuIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  TrendingUp,
  People,
  Article,
  Schedule,
  Close as CloseIcon,
  AccountCircle,
  Logout as LogoutIcon,
  Public as PublicIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon
} from '@mui/icons-material';
import Login from './auth/Login';

const Views = () => {
  const [currentView, setCurrentView] = useState('dashboard');
  const [mobileOpen, setMobileOpen] = useState(false);
  const [newsData, setNewsData] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [selectedNews, setSelectedNews] = useState(null);
  const [jwt, setJwt] = useState(localStorage.getItem('jwt') || '');
  const [checking, setChecking] = useState(true);
  const [anchorEl, setAnchorEl] = useState(null);

  // Table state
  const [selected, setSelected] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  // Dashboard stats with real data
  const dashboardStats = [
    { title: 'Total Berita', value: newsData.length.toString(), icon: <Article />, color: '#2196f3' },
    { title: 'Pengunjung Hari Ini', value: '1,234', icon: <People />, color: '#4caf50' },
    { title: 'Berita Terbaru', value: newsData.filter(news => {
      const newsDate = new Date(news.created_at || news.date);
      const today = new Date();
      const diffTime = Math.abs(today - newsDate);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays <= 7;
    }).length.toString(), icon: <Schedule />, color: '#ff9800' },
    { title: 'Trending', value: newsData.slice(0, 5).length.toString(), icon: <TrendingUp />, color: '#e91e63' }
  ];

  useEffect(() => {
    // Check authentication
    if (!jwt) {
      setChecking(false);
      return;
    }
    setChecking(false);

    // Fetch news data
    fetch('/api/posts')
      .then(res => res.json())
      .then(data => {
        if (Array.isArray(data)) {
          setNewsData(data);
        }
      })
      .catch(() => setNewsData([]));
  }, [jwt]);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleViewChange = (view) => {
    setCurrentView(view);
    if (isMobile) {
      setMobileOpen(false);
    }
  };

  const handleDialog = (type, news = null) => {
    setDialogType(type);
    setSelectedNews(news);
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedNews(null);
  };

  const handleSaveNews = async () => {
    // Handle save news logic here
    // This would typically involve API calls to create/update news
    console.log('Saving news:', selectedNews);
    setDialogOpen(false);
    setSelectedNews(null);

    // Refresh news data
    fetch('/api/posts')
      .then(res => res.json())
      .then(data => {
        if (Array.isArray(data)) {
          setNewsData(data);
        }
      })
      .catch(() => setNewsData([]));
  };

  const handleDeleteNews = async () => {
    if (!selectedNews) return;

    try {
      const token = localStorage.getItem('jwt');
      await fetch(`/api/posts/${selectedNews.id}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });

      // Refresh news data
      fetch('/api/posts')
        .then(res => res.json())
        .then(data => {
          if (Array.isArray(data)) {
            setNewsData(data);
          }
        })
        .catch(() => setNewsData([]));

      setDialogOpen(false);
      setSelectedNews(null);
    } catch (error) {
      console.error('Error deleting news:', error);
    }
  };

  // Table helper functions
  const filteredNews = newsData.filter(news =>
    news.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    news.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const isSelected = (id) => selected.indexOf(id) !== -1;

  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelected = filteredNews.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((n) => n.id);
      setSelected([...new Set([...selected, ...newSelected])]);
    } else {
      const pageIds = filteredNews.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((n) => n.id);
      setSelected(selected.filter(id => !pageIds.includes(id)));
    }
  };

  const handleClick = (id) => {
    const selectedIndex = selected.indexOf(id);
    let newSelected = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1),
      );
    }
    setSelected(newSelected);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    if (isNaN(date)) return dateString;

    const bulan = [
      'Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun',
      'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'
    ];

    const tgl = date.getDate();
    const bln = bulan[date.getMonth()];
    const thn = date.getFullYear();
    const jam = date.getHours().toString().padStart(2, '0');
    const menit = date.getMinutes().toString().padStart(2, '0');

    return `${tgl}/${bln}/${thn} ${jam}:${menit}`;
  };

  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    localStorage.removeItem('jwt');
    setAnchorEl(null);
    window.location.href = '/admin/login';
  };

  const handleViewWebsite = () => {
    window.open('/', '_blank');
    setAnchorEl(null);
  };

  // Show login if not authenticated
  if (!jwt && !checking) {
    return <Login onLogin={token => setJwt(token)} />;
  }

  // Navigation items
  const navigationItems = [
    { id: 'dashboard', label: 'Dashboard', icon: <DashboardIcon /> },
    { id: 'news', label: 'Berita', icon: <NewsIcon /> },
    { id: 'settings', label: 'Pengaturan', icon: <SettingsIcon /> }
  ];

  // Sidebar component
  const Sidebar = () => (
    <Box sx={{ width: 280, height: '100%', bgcolor: 'background.paper', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" fontWeight="bold" color="primary">
          📰 Admin Panel
        </Typography>
      </Box>
      <List sx={{ flex: 1 }}>
        {navigationItems.map((item) => (
          <ListItem
            key={item.id}
            button
            selected={currentView === item.id}
            onClick={() => handleViewChange(item.id)}
            sx={{
              mx: 1,
              my: 0.5,
              borderRadius: 2,
              '&.Mui-selected': {
                bgcolor: 'primary.main',
                color: 'white',
                '&:hover': {
                  bgcolor: 'primary.dark',
                },
                '& .MuiListItemIcon-root': {
                  color: 'white',
                }
              }
            }}
          >
            <ListItemIcon>{item.icon}</ListItemIcon>
            <ListItemText primary={item.label} />
          </ListItem>
        ))}
      </List>

      {/* User Menu in Sidebar */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <List>
          <ListItem button onClick={handleViewWebsite} sx={{ borderRadius: 2 }}>
            <ListItemIcon>
              <PublicIcon />
            </ListItemIcon>
            <ListItemText primary="Lihat Website" />
          </ListItem>
          <ListItem button onClick={handleLogout} sx={{ borderRadius: 2, color: 'error.main' }}>
            <ListItemIcon sx={{ color: 'error.main' }}>
              <LogoutIcon />
            </ListItemIcon>
            <ListItemText primary="Logout" />
          </ListItem>
        </List>
      </Box>
    </Box>
  );

  // Dashboard View
  const DashboardView = () => (
    <Box>
      <Typography variant="h4" fontWeight="bold" mb={3}>
        Dashboard
      </Typography>
      
      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        {dashboardStats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card 
              sx={{ 
                height: '100%',
                background: `linear-gradient(135deg, ${stat.color}15, ${stat.color}05)`,
                border: `1px solid ${stat.color}30`
              }}
            >
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" fontWeight="bold" color={stat.color}>
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {stat.title}
                    </Typography>
                  </Box>
                  <Box sx={{ color: stat.color, opacity: 0.7 }}>
                    {stat.icon}
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Recent News */}
      <Card>
        <CardContent>
          <Typography variant="h6" fontWeight="bold" mb={2}>
            Berita Terbaru
          </Typography>
          <Stack spacing={2}>
            {newsData.slice(0, 5).map((news, index) => (
              <Paper key={index} sx={{ p: 2, bgcolor: 'grey.50' }}>
                <Box display="flex" alignItems="center" gap={2}>
                  <Avatar
                    src={news.image ? `/uploads/${news.image}` : ''}
                    variant="rounded"
                    sx={{ width: 60, height: 60 }}
                  />
                  <Box flex={1}>
                    <Typography variant="subtitle1" fontWeight="medium">
                      {news.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" noWrap>
                      {news.description}
                    </Typography>
                  </Box>
                  <Chip 
                    label="Baru" 
                    size="small" 
                    color="primary" 
                    variant="outlined" 
                  />
                </Box>
              </Paper>
            ))}
          </Stack>
        </CardContent>
      </Card>
    </Box>
  );

  // News View with Responsive Table
  const NewsView = () => {
    const currentPageData = filteredNews.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
    const numSelected = selected.length;
    const rowCount = currentPageData.length;

    return (
      <Box>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" fontWeight="bold">
            Berita
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleDialog('add')}
            sx={{ borderRadius: 2 }}
          >
            Tambah Berita
          </Button>
        </Box>

        {/* Search and Filter */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box display="flex" gap={2} alignItems="center" flexWrap="wrap">
              <TextField
                placeholder="Cari berita..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                size="small"
                sx={{ minWidth: 300, flex: 1 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
              <Button
                variant="outlined"
                startIcon={<FilterListIcon />}
                sx={{ borderRadius: 2 }}
              >
                Filter
              </Button>
              {selected.length > 0 && (
                <Button
                  variant="contained"
                  color="error"
                  onClick={() => {
                    // Handle bulk delete
                    console.log('Delete selected:', selected);
                  }}
                  sx={{ borderRadius: 2 }}
                >
                  Hapus {selected.length} Terpilih
                </Button>
              )}
            </Box>
          </CardContent>
        </Card>

        {/* Table */}
        <Card>
          {isMobile ? (
            /* Mobile Card View */
            <Box sx={{ p: 2 }}>
              {currentPageData.length === 0 ? (
                <Box textAlign="center" py={4}>
                  <Typography variant="h6" color="text.secondary">
                    Tidak ada berita ditemukan
                  </Typography>
                </Box>
              ) : (
                currentPageData.map((news, index) => (
                  <Card key={news.id} sx={{ mb: 2, border: 1, borderColor: 'divider' }}>
                    <CardContent>
                      <Box display="flex" alignItems="center" gap={2} mb={2}>
                        <Checkbox
                          checked={isSelected(news.id)}
                          onChange={() => handleClick(news.id)}
                        />
                        <Chip
                          label={`#${page * rowsPerPage + index + 1}`}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      </Box>

                      <Box display="flex" gap={2} mb={2}>
                        <Avatar
                          src={news.image ? `/uploads/${news.image}` : ''}
                          variant="rounded"
                          sx={{ width: 60, height: 60 }}
                        />
                        <Box flex={1}>
                          <Typography variant="subtitle1" fontWeight="bold" mb={1}>
                            {news.title}
                          </Typography>
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden'
                            }}
                          >
                            {news.description}
                          </Typography>
                        </Box>
                      </Box>

                      <Box display="flex" justifyContent="space-between" alignItems="center">
                        <Typography variant="caption" color="text.secondary">
                          {formatDate(news.created_at || news.date)}
                        </Typography>
                        <Box display="flex" gap={1}>
                          <IconButton
                            size="small"
                            color="info"
                            onClick={() => handleDialog('view', news)}
                          >
                            <ViewIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            color="success"
                            onClick={() => handleDialog('edit', news)}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDialog('delete', news)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                ))
              )}
            </Box>
          ) : (
            /* Desktop Table View */
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow sx={{ bgcolor: 'grey.100' }}>
                    <TableCell padding="checkbox">
                      <Checkbox
                        indeterminate={numSelected > 0 && numSelected < rowCount}
                        checked={rowCount > 0 && numSelected === rowCount}
                        onChange={handleSelectAllClick}
                      />
                    </TableCell>
                    <TableCell>No</TableCell>
                    <TableCell>Gambar</TableCell>
                    <TableCell>Judul</TableCell>
                    <TableCell>Deskripsi</TableCell>
                    <TableCell>Tanggal & Waktu</TableCell>
                    <TableCell align="center">Aksi</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {currentPageData.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                        <Typography variant="h6" color="text.secondary">
                          Tidak ada berita ditemukan
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    currentPageData.map((news, index) => (
                      <TableRow
                        key={news.id}
                        hover
                        selected={isSelected(news.id)}
                      >
                        <TableCell padding="checkbox">
                          <Checkbox
                            checked={isSelected(news.id)}
                            onChange={() => handleClick(news.id)}
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={page * rowsPerPage + index + 1}
                            size="small"
                            color="primary"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          <Avatar
                            src={news.image ? `/uploads/${news.image}` : ''}
                            variant="rounded"
                            sx={{ width: 50, height: 50 }}
                          />
                        </TableCell>
                        <TableCell sx={{ maxWidth: 200 }}>
                          <Typography variant="body2" fontWeight="medium">
                            {news.title}
                          </Typography>
                        </TableCell>
                        <TableCell sx={{ maxWidth: 300 }}>
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden'
                            }}
                          >
                            {news.description}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="caption" color="text.secondary">
                            {formatDate(news.created_at || news.date)}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <Box display="flex" gap={0.5} justifyContent="center">
                            <IconButton
                              size="small"
                              color="info"
                              onClick={() => handleDialog('view', news)}
                            >
                              <ViewIcon />
                            </IconButton>
                            <IconButton
                              size="small"
                              color="success"
                              onClick={() => handleDialog('edit', news)}
                            >
                              <EditIcon />
                            </IconButton>
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleDialog('delete', news)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}

          {/* Pagination */}
          <TablePagination
            component="div"
            count={filteredNews.length}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={[5, 10, 25, 50]}
            labelRowsPerPage="Baris per halaman:"
            labelDisplayedRows={({ from, to, count }) =>
              `${from}-${to} dari ${count !== -1 ? count : `lebih dari ${to}`}`
            }
          />
        </Card>
      </Box>
    );
  };

  // Settings View
  const SettingsView = () => (
    <Box>
      <Typography variant="h4" fontWeight="bold" mb={3}>
        Pengaturan
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" mb={2}>
                Pengaturan Umum
              </Typography>
              <Stack spacing={3}>
                <TextField
                  fullWidth
                  label="Nama Website"
                  defaultValue="React News"
                  variant="outlined"
                />
                <TextField
                  fullWidth
                  label="Deskripsi Website"
                  defaultValue="Portal berita terkini"
                  variant="outlined"
                  multiline
                  rows={3}
                />
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="Aktifkan Notifikasi"
                />
                <FormControlLabel
                  control={<Switch />}
                  label="Mode Maintenance"
                />
              </Stack>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" mb={2}>
                Pengaturan Tampilan
              </Typography>
              <Stack spacing={3}>
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="Mode Gelap"
                />
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="Sidebar Otomatis"
                />
                <TextField
                  fullWidth
                  label="Jumlah Berita per Halaman"
                  type="number"
                  defaultValue="10"
                  variant="outlined"
                />
                <Button variant="contained" sx={{ borderRadius: 2 }}>
                  Simpan Pengaturan
                </Button>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  const renderCurrentView = () => {
    switch (currentView) {
      case 'dashboard':
        return <DashboardView />;
      case 'news':
        return <NewsView />;
      case 'settings':
        return <SettingsView />;
      default:
        return <DashboardView />;
    }
  };

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', bgcolor: 'grey.50' }}>
      {/* Mobile App Bar */}
      {isMobile && (
        <AppBar position="fixed" sx={{ zIndex: theme.zIndex.drawer + 1 }}>
          <Toolbar>
            <IconButton
              color="inherit"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2 }}
            >
              <MenuIcon />
            </IconButton>
            <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
              {navigationItems.find(item => item.id === currentView)?.label}
            </Typography>
            <IconButton
              color="inherit"
              onClick={handleMenu}
            >
              <AccountCircle />
            </IconButton>
          </Toolbar>
        </AppBar>
      )}

      {/* User Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseMenu}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem onClick={handleViewWebsite}>
          <ListItemIcon>
            <PublicIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Lihat Website</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleLogout} sx={{ color: 'error.main' }}>
          <ListItemIcon sx={{ color: 'error.main' }}>
            <LogoutIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Logout</ListItemText>
        </MenuItem>
      </Menu>

      {/* Sidebar */}
      {isMobile ? (
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: 280 },
          }}
        >
          <Sidebar />
        </Drawer>
      ) : (
        <Drawer
          variant="permanent"
          sx={{
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: 280,
              position: 'relative',
            },
          }}
        >
          <Sidebar />
        </Drawer>
      )}

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          mt: isMobile ? 8 : 0,
          width: { md: `calc(100% - 280px)` }
        }}
      >
        {renderCurrentView()}
      </Box>

      {/* Mobile FAB for adding news */}
      {isMobile && currentView === 'news' && (
        <Fab
          color="primary"
          sx={{ position: 'fixed', bottom: 16, right: 16 }}
          onClick={() => handleDialog('add')}
        >
          <AddIcon />
        </Fab>
      )}

      {/* Dialog for news actions */}
      <Dialog 
        open={dialogOpen} 
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        fullScreen={isMobile}
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">
              {dialogType === 'add' && 'Tambah Berita Baru'}
              {dialogType === 'edit' && 'Edit Berita'}
              {dialogType === 'view' && 'Detail Berita'}
              {dialogType === 'delete' && 'Hapus Berita'}
            </Typography>
            {isMobile && (
              <IconButton onClick={handleCloseDialog}>
                <CloseIcon />
              </IconButton>
            )}
          </Box>
        </DialogTitle>
        <DialogContent>
          {dialogType === 'delete' ? (
            <Typography>
              Apakah Anda yakin ingin menghapus berita "{selectedNews?.title}"?
            </Typography>
          ) : (
            <Stack spacing={3} sx={{ mt: 1 }}>
              <TextField
                fullWidth
                label="Judul Berita"
                defaultValue={selectedNews?.title || ''}
                disabled={dialogType === 'view'}
              />
              <TextField
                fullWidth
                label="Deskripsi"
                multiline
                rows={4}
                defaultValue={selectedNews?.description || ''}
                disabled={dialogType === 'view'}
              />
              {dialogType !== 'view' && (
                <Button variant="outlined" component="label">
                  Upload Gambar
                  <input type="file" hidden accept="image/*" />
                </Button>
              )}
            </Stack>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            {dialogType === 'view' ? 'Tutup' : 'Batal'}
          </Button>
          {dialogType !== 'view' && (
            <Button
              variant="contained"
              onClick={dialogType === 'delete' ? handleDeleteNews : handleSaveNews}
              color={dialogType === 'delete' ? 'error' : 'primary'}
            >
              {dialogType === 'delete' ? 'Hapus' : 'Simpan'}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Views;
