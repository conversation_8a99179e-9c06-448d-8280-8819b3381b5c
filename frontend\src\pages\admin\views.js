import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  IconButton,
  Avatar,
  Chip,
  Button,
  TextField,
  Switch,
  FormControlLabel,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  AppBar,
  Toolbar,
  useMediaQuery,
  useTheme,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  Paper,
  Stack,
  Menu,
  MenuItem,
  Table,
  TableBody,
  TableCell,

  TableHead,
  TableRow,
  Checkbox,
  TablePagination,
  InputAdornment
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Article as NewsIcon,
  Settings as SettingsIcon,
  Menu as MenuIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  TrendingUp,
  People,
  Article,
  Schedule,
  Close as CloseIcon,
  AccountCircle,
  Logout as LogoutIcon,
  Public as PublicIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon
} from '@mui/icons-material';
import Login from './auth/Login';

const Views = () => {
  const [currentView, setCurrentView] = useState('dashboard');
  const [mobileOpen, setMobileOpen] = useState(false);
  const [newsData, setNewsData] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [selectedNews, setSelectedNews] = useState(null);
  const [jwt, setJwt] = useState(localStorage.getItem('jwt') || '');
  const [checking, setChecking] = useState(true);
  const [anchorEl, setAnchorEl] = useState(null);

  // Table state
  const [selected, setSelected] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  // Dashboard stats with real data
  const dashboardStats = [
    { title: 'Total Berita', value: newsData.length.toString(), icon: <Article />, color: '#2196f3' },
    { title: 'Pengunjung Hari Ini', value: '1,234', icon: <People />, color: '#4caf50' },
    { title: 'Berita Terbaru', value: newsData.filter(news => {
      const newsDate = new Date(news.created_at || news.date);
      const today = new Date();
      const diffTime = Math.abs(today - newsDate);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays <= 7;
    }).length.toString(), icon: <Schedule />, color: '#ff9800' },
    { title: 'Trending', value: newsData.slice(0, 5).length.toString(), icon: <TrendingUp />, color: '#e91e63' }
  ];

  useEffect(() => {
    // Check authentication
    if (!jwt) {
      setChecking(false);
      return;
    }
    setChecking(false);

    // Fetch news data
    fetch('/api/posts')
      .then(res => res.json())
      .then(data => {
        if (Array.isArray(data)) {
          setNewsData(data);
        }
      })
      .catch(() => setNewsData([]));
  }, [jwt]);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleViewChange = (view) => {
    setCurrentView(view);
    if (isMobile) {
      setMobileOpen(false);
    }
  };

  const handleDialog = (type, news = null) => {
    setDialogType(type);
    setSelectedNews(news);
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedNews(null);
  };

  const handleSaveNews = async () => {
    // Handle save news logic here
    // This would typically involve API calls to create/update news
    console.log('Saving news:', selectedNews);
    setDialogOpen(false);
    setSelectedNews(null);

    // Refresh news data
    fetch('/api/posts')
      .then(res => res.json())
      .then(data => {
        if (Array.isArray(data)) {
          setNewsData(data);
        }
      })
      .catch(() => setNewsData([]));
  };

  const handleDeleteNews = async () => {
    if (!selectedNews) return;

    try {
      const token = localStorage.getItem('jwt');
      await fetch(`/api/posts/${selectedNews.id}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });

      // Refresh news data
      fetch('/api/posts')
        .then(res => res.json())
        .then(data => {
          if (Array.isArray(data)) {
            setNewsData(data);
          }
        })
        .catch(() => setNewsData([]));

      setDialogOpen(false);
      setSelectedNews(null);
    } catch (error) {
      console.error('Error deleting news:', error);
    }
  };

  // Table helper functions
  const filteredNews = newsData.filter(news =>
    news.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    news.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const isSelected = (id) => selected.indexOf(id) !== -1;

  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelected = filteredNews.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((n) => n.id);
      setSelected([...new Set([...selected, ...newSelected])]);
    } else {
      const pageIds = filteredNews.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((n) => n.id);
      setSelected(selected.filter(id => !pageIds.includes(id)));
    }
  };

  const handleClick = (id) => {
    const selectedIndex = selected.indexOf(id);
    let newSelected = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1));
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1),
      );
    }
    setSelected(newSelected);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    if (isNaN(date)) return dateString;

    const bulan = [
      'Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun',
      'Jul', 'Ags', 'Sep', 'Okt', 'Nov', 'Des'
    ];

    const tgl = date.getDate();
    const bln = bulan[date.getMonth()];
    const thn = date.getFullYear();
    const jam = date.getHours().toString().padStart(2, '0');
    const menit = date.getMinutes().toString().padStart(2, '0');

    return `${tgl}/${bln}/${thn} ${jam}:${menit}`;
  };

  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    localStorage.removeItem('jwt');
    setAnchorEl(null);
    window.location.href = '/admin/login';
  };

  const handleViewWebsite = () => {
    window.open('/', '_blank');
    setAnchorEl(null);
  };

  // Show login if not authenticated
  if (!jwt && !checking) {
    return <Login onLogin={token => setJwt(token)} />;
  }

  // Navigation items
  const navigationItems = [
    { id: 'dashboard', label: 'Dashboard', icon: <DashboardIcon /> },
    { id: 'news', label: 'Berita', icon: <NewsIcon /> },
    { id: 'settings', label: 'Pengaturan', icon: <SettingsIcon /> }
  ];

  // Sidebar component
  const Sidebar = () => (
    <Box sx={{ width: 280, height: '100%', bgcolor: 'background.paper', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" fontWeight="bold" color="primary">
          📰 Admin Panel
        </Typography>
      </Box>
      <List sx={{ flex: 1 }}>
        {navigationItems.map((item) => (
          <ListItem
            key={item.id}
            button
            selected={currentView === item.id}
            onClick={() => handleViewChange(item.id)}
            sx={{
              mx: 1,
              my: 0.5,
              borderRadius: 2,
              '&.Mui-selected': {
                bgcolor: 'primary.main',
                color: 'white',
                '&:hover': {
                  bgcolor: 'primary.dark',
                },
                '& .MuiListItemIcon-root': {
                  color: 'white',
                }
              }
            }}
          >
            <ListItemIcon>{item.icon}</ListItemIcon>
            <ListItemText primary={item.label} />
          </ListItem>
        ))}
      </List>

      {/* User Menu in Sidebar */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <List>
          <ListItem button onClick={handleViewWebsite} sx={{ borderRadius: 2 }}>
            <ListItemIcon>
              <PublicIcon />
            </ListItemIcon>
            <ListItemText primary="Lihat Website" />
          </ListItem>
          <ListItem button onClick={handleLogout} sx={{ borderRadius: 2, color: 'error.main' }}>
            <ListItemIcon sx={{ color: 'error.main' }}>
              <LogoutIcon />
            </ListItemIcon>
            <ListItemText primary="Logout" />
          </ListItem>
        </List>
      </Box>
    </Box>
  );

  // Dashboard View
  const DashboardView = () => (
    <Box>
      <Typography variant="h4" fontWeight="bold" mb={3}>
        Dashboard
      </Typography>
      
      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        {dashboardStats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card 
              sx={{ 
                height: '100%',
                background: `linear-gradient(135deg, ${stat.color}15, ${stat.color}05)`,
                border: `1px solid ${stat.color}30`
              }}
            >
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" fontWeight="bold" color={stat.color}>
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {stat.title}
                    </Typography>
                  </Box>
                  <Box sx={{ color: stat.color, opacity: 0.7 }}>
                    {stat.icon}
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Recent News */}
      <Card>
        <CardContent>
          <Typography variant="h6" fontWeight="bold" mb={2}>
            Berita Terbaru
          </Typography>
          <Stack spacing={2}>
            {newsData.slice(0, 5).map((news, index) => (
              <Paper key={index} sx={{ p: 2, bgcolor: 'grey.50' }}>
                <Box display="flex" alignItems="center" gap={2}>
                  <Avatar
                    src={news.image ? `/uploads/${news.image}` : ''}
                    variant="rounded"
                    sx={{ width: 60, height: 60 }}
                  />
                  <Box flex={1}>
                    <Typography variant="subtitle1" fontWeight="medium">
                      {news.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" noWrap>
                      {news.description}
                    </Typography>
                  </Box>
                  <Chip 
                    label="Baru" 
                    size="small" 
                    color="primary" 
                    variant="outlined" 
                  />
                </Box>
              </Paper>
            ))}
          </Stack>
        </CardContent>
      </Card>
    </Box>
  );

  // News View with Responsive Table
  const NewsView = () => {
    const currentPageData = filteredNews.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
    const numSelected = selected.length;
    const rowCount = currentPageData.length;

    return (
      <Box>
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" fontWeight="bold">
            Berita
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleDialog('add')}
            sx={{ borderRadius: 2 }}
          >
            Tambah Berita
          </Button>
        </Box>

        {/* Search and Filter */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box display="flex" gap={2} alignItems="center" flexWrap="wrap">
              <TextField
                placeholder="Cari berita..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                size="small"
                sx={{ minWidth: 300, flex: 1 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
              <Button
                variant="outlined"
                startIcon={<FilterListIcon />}
                sx={{ borderRadius: 2 }}
              >
                Filter
              </Button>
              {selected.length > 0 && (
                <Button
                  variant="contained"
                  color="error"
                  onClick={() => {
                    // Handle bulk delete
                    console.log('Delete selected:', selected);
                  }}
                  sx={{ borderRadius: 2 }}
                >
                  Hapus {selected.length} Terpilih
                </Button>
              )}
            </Box>
          </CardContent>
        </Card>

        {/* Table */}
        <Card>
          {/* Table Container with Horizontal Scroll */}
          <Box
            sx={{
              overflowX: 'auto',
              overflowY: 'hidden',
              '&::-webkit-scrollbar': {
                height: 8,
              },
              '&::-webkit-scrollbar-track': {
                backgroundColor: '#f1f1f1',
                borderRadius: 4,
                margin: '0 16px',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: '#c1c1c1',
                borderRadius: 4,
                '&:hover': {
                  backgroundColor: '#a8a8a8',
                },
              },
            }}
          >
            <Table sx={{ minWidth: 800, width: '100%' }}>
              <TableHead>
                <TableRow sx={{ bgcolor: 'grey.100' }}>
                  <TableCell
                    padding="checkbox"
                    sx={{
                      minWidth: 60,
                      width: 60,
                      fontSize: isMobile ? '0.75rem' : '0.875rem',
                      fontWeight: 'bold'
                    }}
                  >
                    <Checkbox
                      indeterminate={numSelected > 0 && numSelected < rowCount}
                      checked={rowCount > 0 && numSelected === rowCount}
                      onChange={handleSelectAllClick}
                      size={isMobile ? 'small' : 'medium'}
                    />
                  </TableCell>
                  <TableCell
                    sx={{
                      minWidth: 80,
                      width: 80,
                      fontSize: isMobile ? '0.75rem' : '0.875rem',
                      fontWeight: 'bold'
                    }}
                  >
                    No
                  </TableCell>
                  <TableCell
                    sx={{
                      minWidth: 100,
                      width: 100,
                      fontSize: isMobile ? '0.75rem' : '0.875rem',
                      fontWeight: 'bold'
                    }}
                  >
                    Gambar
                  </TableCell>
                  <TableCell
                    sx={{
                      minWidth: 200,
                      width: 200,
                      fontSize: isMobile ? '0.75rem' : '0.875rem',
                      fontWeight: 'bold'
                    }}
                  >
                    Judul
                  </TableCell>
                  <TableCell
                    sx={{
                      minWidth: 250,
                      width: 250,
                      fontSize: isMobile ? '0.75rem' : '0.875rem',
                      fontWeight: 'bold'
                    }}
                  >
                    Deskripsi
                  </TableCell>
                  <TableCell
                    sx={{
                      minWidth: 140,
                      width: 140,
                      fontSize: isMobile ? '0.75rem' : '0.875rem',
                      fontWeight: 'bold'
                    }}
                  >
                    Tanggal & Waktu
                  </TableCell>
                  <TableCell
                    align="center"
                    sx={{
                      minWidth: 130,
                      width: 130,
                      fontSize: isMobile ? '0.75rem' : '0.875rem',
                      fontWeight: 'bold'
                    }}
                  >
                    Aksi
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {currentPageData.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                      <Typography variant="h6" color="text.secondary">
                        Tidak ada berita ditemukan
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  currentPageData.map((news, index) => (
                    <TableRow
                      key={news.id}
                      hover
                      selected={isSelected(news.id)}
                      sx={{
                        '&:nth-of-type(odd)': {
                          backgroundColor: 'rgba(0, 0, 0, 0.02)',
                        },
                      }}
                    >
                      <TableCell
                        padding="checkbox"
                        sx={{
                          position: isMobile ? 'sticky' : 'static',
                          left: isMobile ? 0 : 'auto',
                          bgcolor: isSelected(news.id) ? 'action.selected' : 'background.paper',
                          zIndex: 1
                        }}
                      >
                        <Checkbox
                          checked={isSelected(news.id)}
                          onChange={() => handleClick(news.id)}
                          size={isMobile ? 'small' : 'medium'}
                        />
                      </TableCell>
                      <TableCell sx={{ py: isMobile ? 1 : 2 }}>
                        <Chip
                          label={page * rowsPerPage + index + 1}
                          size="small"
                          color="primary"
                          variant="outlined"
                          sx={{ fontSize: isMobile ? '0.7rem' : '0.75rem' }}
                        />
                      </TableCell>
                      <TableCell sx={{ py: isMobile ? 1 : 2 }}>
                        <Avatar
                          src={news.image ? `/uploads/${news.image}` : ''}
                          variant="rounded"
                          sx={{
                            width: isMobile ? 40 : 50,
                            height: isMobile ? 40 : 50,
                            border: 1,
                            borderColor: 'divider'
                          }}
                        />
                      </TableCell>
                      <TableCell sx={{ py: isMobile ? 1 : 2 }}>
                        <Typography
                          variant={isMobile ? 'caption' : 'body2'}
                          fontWeight="medium"
                          sx={{
                            display: '-webkit-box',
                            WebkitLineClamp: isMobile ? 2 : 3,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            lineHeight: 1.2
                          }}
                        >
                          {news.title}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ py: isMobile ? 1 : 2 }}>
                        <Typography
                          variant={isMobile ? 'caption' : 'body2'}
                          color="text.secondary"
                          sx={{
                            display: '-webkit-box',
                            WebkitLineClamp: isMobile ? 2 : 3,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            lineHeight: 1.2
                          }}
                        >
                          {news.description}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ py: isMobile ? 1 : 2 }}>
                        <Typography
                          variant="caption"
                          color="text.secondary"
                          sx={{
                            whiteSpace: 'nowrap',
                            fontSize: isMobile ? '0.65rem' : '0.75rem'
                          }}
                        >
                          {formatDate(news.created_at || news.date)}
                        </Typography>
                      </TableCell>
                      <TableCell align="center" sx={{ py: isMobile ? 1 : 2 }}>
                        <Box display="flex" gap={0.5} justifyContent="center">
                          <IconButton
                            size="small"
                            color="info"
                            onClick={() => handleDialog('view', news)}
                            sx={{
                              p: isMobile ? 0.5 : 1,
                              bgcolor: 'info.light',
                              color: 'info.contrastText',
                              '&:hover': {
                                bgcolor: 'info.main',
                              }
                            }}
                          >
                            <ViewIcon sx={{ fontSize: isMobile ? 16 : 20 }} />
                          </IconButton>
                          <IconButton
                            size="small"
                            color="success"
                            onClick={() => handleDialog('edit', news)}
                            sx={{
                              p: isMobile ? 0.5 : 1,
                              bgcolor: 'success.light',
                              color: 'success.contrastText',
                              '&:hover': {
                                bgcolor: 'success.main',
                              }
                            }}
                          >
                            <EditIcon sx={{ fontSize: isMobile ? 16 : 20 }} />
                          </IconButton>
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDialog('delete', news)}
                            sx={{
                              p: isMobile ? 0.5 : 1,
                              bgcolor: 'error.light',
                              color: 'error.contrastText',
                              '&:hover': {
                                bgcolor: 'error.main',
                              }
                            }}
                          >
                            <DeleteIcon sx={{ fontSize: isMobile ? 16 : 20 }} />
                          </IconButton>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </Box>

          {/* Pagination - Outside scroll container */}
          <TablePagination
            component="div"
            count={filteredNews.length}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={[5, 10, 25, 50]}
            labelRowsPerPage="Baris per halaman:"
            labelDisplayedRows={({ from, to, count }) =>
              `${from}-${to} dari ${count !== -1 ? count : `lebih dari ${to}`}`
            }
            sx={{
              borderTop: 1,
              borderColor: 'divider',
              bgcolor: 'background.paper'
            }}
          />
        </Card>
      </Box>
    );
  };

  // Settings View
  const SettingsView = () => (
    <Box>
      <Typography variant="h4" fontWeight="bold" mb={3}>
        Pengaturan
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" mb={2}>
                Pengaturan Umum
              </Typography>
              <Stack spacing={3}>
                <TextField
                  fullWidth
                  label="Nama Website"
                  defaultValue="React News"
                  variant="outlined"
                />
                <TextField
                  fullWidth
                  label="Deskripsi Website"
                  defaultValue="Portal berita terkini"
                  variant="outlined"
                  multiline
                  rows={3}
                />
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="Aktifkan Notifikasi"
                />
                <FormControlLabel
                  control={<Switch />}
                  label="Mode Maintenance"
                />
              </Stack>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" mb={2}>
                Pengaturan Tampilan
              </Typography>
              <Stack spacing={3}>
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="Mode Gelap"
                />
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="Sidebar Otomatis"
                />
                <TextField
                  fullWidth
                  label="Jumlah Berita per Halaman"
                  type="number"
                  defaultValue="10"
                  variant="outlined"
                />
                <Button variant="contained" sx={{ borderRadius: 2 }}>
                  Simpan Pengaturan
                </Button>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  const renderCurrentView = () => {
    switch (currentView) {
      case 'dashboard':
        return <DashboardView />;
      case 'news':
        return <NewsView />;
      case 'settings':
        return <SettingsView />;
      default:
        return <DashboardView />;
    }
  };

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', bgcolor: 'grey.50' }}>
      {/* Mobile App Bar */}
      {isMobile && (
        <AppBar position="fixed" sx={{ zIndex: theme.zIndex.drawer + 1 }}>
          <Toolbar>
            <IconButton
              color="inherit"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2 }}
            >
              <MenuIcon />
            </IconButton>
            <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
              {navigationItems.find(item => item.id === currentView)?.label}
            </Typography>
            <IconButton
              color="inherit"
              onClick={handleMenu}
            >
              <AccountCircle />
            </IconButton>
          </Toolbar>
        </AppBar>
      )}

      {/* User Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleCloseMenu}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem onClick={handleViewWebsite}>
          <ListItemIcon>
            <PublicIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Lihat Website</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleLogout} sx={{ color: 'error.main' }}>
          <ListItemIcon sx={{ color: 'error.main' }}>
            <LogoutIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Logout</ListItemText>
        </MenuItem>
      </Menu>

      {/* Sidebar */}
      {isMobile ? (
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: 280 },
          }}
        >
          <Sidebar />
        </Drawer>
      ) : (
        <Drawer
          variant="permanent"
          sx={{
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: 280,
              position: 'relative',
            },
          }}
        >
          <Sidebar />
        </Drawer>
      )}

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          mt: isMobile ? 8 : 0,
          width: { md: `calc(100% - 280px)` }
        }}
      >
        {renderCurrentView()}
      </Box>

      {/* Mobile FAB for adding news */}
      {isMobile && currentView === 'news' && (
        <Fab
          color="primary"
          sx={{ position: 'fixed', bottom: 16, right: 16 }}
          onClick={() => handleDialog('add')}
        >
          <AddIcon />
        </Fab>
      )}

      {/* Dialog for news actions */}
      <Dialog 
        open={dialogOpen} 
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        fullScreen={isMobile}
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">
              {dialogType === 'add' && 'Tambah Berita Baru'}
              {dialogType === 'edit' && 'Edit Berita'}
              {dialogType === 'view' && 'Detail Berita'}
              {dialogType === 'delete' && 'Hapus Berita'}
            </Typography>
            {isMobile && (
              <IconButton onClick={handleCloseDialog}>
                <CloseIcon />
              </IconButton>
            )}
          </Box>
        </DialogTitle>
        <DialogContent>
          {dialogType === 'delete' ? (
            <Typography>
              Apakah Anda yakin ingin menghapus berita "{selectedNews?.title}"?
            </Typography>
          ) : (
            <Stack spacing={3} sx={{ mt: 1 }}>
              <TextField
                fullWidth
                label="Judul Berita"
                defaultValue={selectedNews?.title || ''}
                disabled={dialogType === 'view'}
              />
              <TextField
                fullWidth
                label="Deskripsi"
                multiline
                rows={4}
                defaultValue={selectedNews?.description || ''}
                disabled={dialogType === 'view'}
              />
              {dialogType !== 'view' && (
                <Button variant="outlined" component="label">
                  Upload Gambar
                  <input type="file" hidden accept="image/*" />
                </Button>
              )}
            </Stack>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            {dialogType === 'view' ? 'Tutup' : 'Batal'}
          </Button>
          {dialogType !== 'view' && (
            <Button
              variant="contained"
              onClick={dialogType === 'delete' ? handleDeleteNews : handleSaveNews}
              color={dialogType === 'delete' ? 'error' : 'primary'}
            >
              {dialogType === 'delete' ? 'Hapus' : 'Simpan'}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Views;
