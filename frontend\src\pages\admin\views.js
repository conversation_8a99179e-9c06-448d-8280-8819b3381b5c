import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  IconButton,
  Avatar,
  Chip,
  Button,
  TextField,
  Switch,
  FormControlLabel,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  AppBar,
  Toolbar,
  useMediaQuery,
  useTheme,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  Paper,
  Stack
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Article as NewsIcon,
  Settings as SettingsIcon,
  Menu as MenuIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  TrendingUp,
  People,
  Article,
  Schedule,
  Close as CloseIcon
} from '@mui/icons-material';

const Views = () => {
  const [currentView, setCurrentView] = useState('dashboard');
  const [mobileOpen, setMobileOpen] = useState(false);
  const [newsData, setNewsData] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [selectedNews, setSelectedNews] = useState(null);
  
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  // Sample data
  const dashboardStats = [
    { title: 'Total Berita', value: '24', icon: <Article />, color: '#2196f3' },
    { title: 'Pengunjung Hari Ini', value: '1,234', icon: <People />, color: '#4caf50' },
    { title: 'Berita Terbaru', value: '8', icon: <Schedule />, color: '#ff9800' },
    { title: 'Trending', value: '12', icon: <TrendingUp />, color: '#e91e63' }
  ];

  useEffect(() => {
    // Fetch news data
    fetch('/api/posts')
      .then(res => res.json())
      .then(data => {
        if (Array.isArray(data)) {
          setNewsData(data);
        }
      })
      .catch(() => setNewsData([]));
  }, []);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleViewChange = (view) => {
    setCurrentView(view);
    if (isMobile) {
      setMobileOpen(false);
    }
  };

  const handleDialog = (type, news = null) => {
    setDialogType(type);
    setSelectedNews(news);
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedNews(null);
  };

  // Navigation items
  const navigationItems = [
    { id: 'dashboard', label: 'Dashboard', icon: <DashboardIcon /> },
    { id: 'news', label: 'Berita', icon: <NewsIcon /> },
    { id: 'settings', label: 'Pengaturan', icon: <SettingsIcon /> }
  ];

  // Sidebar component
  const Sidebar = () => (
    <Box sx={{ width: 280, height: '100%', bgcolor: 'background.paper' }}>
      <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" fontWeight="bold" color="primary">
          Admin Panel
        </Typography>
      </Box>
      <List>
        {navigationItems.map((item) => (
          <ListItem
            key={item.id}
            button
            selected={currentView === item.id}
            onClick={() => handleViewChange(item.id)}
            sx={{
              mx: 1,
              my: 0.5,
              borderRadius: 2,
              '&.Mui-selected': {
                bgcolor: 'primary.main',
                color: 'white',
                '&:hover': {
                  bgcolor: 'primary.dark',
                },
                '& .MuiListItemIcon-root': {
                  color: 'white',
                }
              }
            }}
          >
            <ListItemIcon>{item.icon}</ListItemIcon>
            <ListItemText primary={item.label} />
          </ListItem>
        ))}
      </List>
    </Box>
  );

  // Dashboard View
  const DashboardView = () => (
    <Box>
      <Typography variant="h4" fontWeight="bold" mb={3}>
        Dashboard
      </Typography>
      
      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        {dashboardStats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card 
              sx={{ 
                height: '100%',
                background: `linear-gradient(135deg, ${stat.color}15, ${stat.color}05)`,
                border: `1px solid ${stat.color}30`
              }}
            >
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" fontWeight="bold" color={stat.color}>
                      {stat.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {stat.title}
                    </Typography>
                  </Box>
                  <Box sx={{ color: stat.color, opacity: 0.7 }}>
                    {stat.icon}
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Recent News */}
      <Card>
        <CardContent>
          <Typography variant="h6" fontWeight="bold" mb={2}>
            Berita Terbaru
          </Typography>
          <Stack spacing={2}>
            {newsData.slice(0, 5).map((news, index) => (
              <Paper key={index} sx={{ p: 2, bgcolor: 'grey.50' }}>
                <Box display="flex" alignItems="center" gap={2}>
                  <Avatar
                    src={news.image ? `/uploads/${news.image}` : ''}
                    variant="rounded"
                    sx={{ width: 60, height: 60 }}
                  />
                  <Box flex={1}>
                    <Typography variant="subtitle1" fontWeight="medium">
                      {news.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" noWrap>
                      {news.description}
                    </Typography>
                  </Box>
                  <Chip 
                    label="Baru" 
                    size="small" 
                    color="primary" 
                    variant="outlined" 
                  />
                </Box>
              </Paper>
            ))}
          </Stack>
        </CardContent>
      </Card>
    </Box>
  );

  // News View
  const NewsView = () => (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" fontWeight="bold">
          Berita
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleDialog('add')}
          sx={{ borderRadius: 2 }}
        >
          Tambah Berita
        </Button>
      </Box>

      {/* News Grid */}
      <Grid container spacing={3}>
        {newsData.map((news, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <Avatar
                src={news.image ? `/uploads/${news.image}` : ''}
                variant="square"
                sx={{ width: '100%', height: 200 }}
              />
              <CardContent sx={{ flex: 1 }}>
                <Typography variant="h6" fontWeight="bold" mb={1} noWrap>
                  {news.title}
                </Typography>
                <Typography 
                  variant="body2" 
                  color="text.secondary" 
                  sx={{ 
                    display: '-webkit-box',
                    WebkitLineClamp: 3,
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden'
                  }}
                >
                  {news.description}
                </Typography>
              </CardContent>
              <Box sx={{ p: 2, pt: 0 }}>
                <Box display="flex" gap={1}>
                  <IconButton 
                    size="small" 
                    color="primary"
                    onClick={() => handleDialog('view', news)}
                  >
                    <ViewIcon />
                  </IconButton>
                  <IconButton 
                    size="small" 
                    color="success"
                    onClick={() => handleDialog('edit', news)}
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton 
                    size="small" 
                    color="error"
                    onClick={() => handleDialog('delete', news)}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
              </Box>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );

  // Settings View
  const SettingsView = () => (
    <Box>
      <Typography variant="h4" fontWeight="bold" mb={3}>
        Pengaturan
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" mb={2}>
                Pengaturan Umum
              </Typography>
              <Stack spacing={3}>
                <TextField
                  fullWidth
                  label="Nama Website"
                  defaultValue="React News"
                  variant="outlined"
                />
                <TextField
                  fullWidth
                  label="Deskripsi Website"
                  defaultValue="Portal berita terkini"
                  variant="outlined"
                  multiline
                  rows={3}
                />
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="Aktifkan Notifikasi"
                />
                <FormControlLabel
                  control={<Switch />}
                  label="Mode Maintenance"
                />
              </Stack>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" mb={2}>
                Pengaturan Tampilan
              </Typography>
              <Stack spacing={3}>
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="Mode Gelap"
                />
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="Sidebar Otomatis"
                />
                <TextField
                  fullWidth
                  label="Jumlah Berita per Halaman"
                  type="number"
                  defaultValue="10"
                  variant="outlined"
                />
                <Button variant="contained" sx={{ borderRadius: 2 }}>
                  Simpan Pengaturan
                </Button>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );

  const renderCurrentView = () => {
    switch (currentView) {
      case 'dashboard':
        return <DashboardView />;
      case 'news':
        return <NewsView />;
      case 'settings':
        return <SettingsView />;
      default:
        return <DashboardView />;
    }
  };

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', bgcolor: 'grey.50' }}>
      {/* Mobile App Bar */}
      {isMobile && (
        <AppBar position="fixed" sx={{ zIndex: theme.zIndex.drawer + 1 }}>
          <Toolbar>
            <IconButton
              color="inherit"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2 }}
            >
              <MenuIcon />
            </IconButton>
            <Typography variant="h6" noWrap component="div">
              {navigationItems.find(item => item.id === currentView)?.label}
            </Typography>
          </Toolbar>
        </AppBar>
      )}

      {/* Sidebar */}
      {isMobile ? (
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: 280 },
          }}
        >
          <Sidebar />
        </Drawer>
      ) : (
        <Drawer
          variant="permanent"
          sx={{
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: 280,
              position: 'relative',
            },
          }}
        >
          <Sidebar />
        </Drawer>
      )}

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          mt: isMobile ? 8 : 0,
          width: { md: `calc(100% - 280px)` }
        }}
      >
        {renderCurrentView()}
      </Box>

      {/* Mobile FAB for adding news */}
      {isMobile && currentView === 'news' && (
        <Fab
          color="primary"
          sx={{ position: 'fixed', bottom: 16, right: 16 }}
          onClick={() => handleDialog('add')}
        >
          <AddIcon />
        </Fab>
      )}

      {/* Dialog for news actions */}
      <Dialog 
        open={dialogOpen} 
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        fullScreen={isMobile}
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">
              {dialogType === 'add' && 'Tambah Berita Baru'}
              {dialogType === 'edit' && 'Edit Berita'}
              {dialogType === 'view' && 'Detail Berita'}
              {dialogType === 'delete' && 'Hapus Berita'}
            </Typography>
            {isMobile && (
              <IconButton onClick={handleCloseDialog}>
                <CloseIcon />
              </IconButton>
            )}
          </Box>
        </DialogTitle>
        <DialogContent>
          {dialogType === 'delete' ? (
            <Typography>
              Apakah Anda yakin ingin menghapus berita "{selectedNews?.title}"?
            </Typography>
          ) : (
            <Stack spacing={3} sx={{ mt: 1 }}>
              <TextField
                fullWidth
                label="Judul Berita"
                defaultValue={selectedNews?.title || ''}
                disabled={dialogType === 'view'}
              />
              <TextField
                fullWidth
                label="Deskripsi"
                multiline
                rows={4}
                defaultValue={selectedNews?.description || ''}
                disabled={dialogType === 'view'}
              />
              {dialogType !== 'view' && (
                <Button variant="outlined" component="label">
                  Upload Gambar
                  <input type="file" hidden accept="image/*" />
                </Button>
              )}
            </Stack>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            {dialogType === 'view' ? 'Tutup' : 'Batal'}
          </Button>
          {dialogType !== 'view' && (
            <Button variant="contained" onClick={handleCloseDialog}>
              {dialogType === 'delete' ? 'Hapus' : 'Simpan'}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Views;
